import React from 'react';
import {
  <PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON>rid,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip
} from '@mui/material';
import {
  School,
  PlayArrow,
  Code,
  SmartToy,
  Psychology,
  Engineering
} from '@mui/icons-material';

function Education({ addNotification }) {
  const tutorials = [
    {
      id: 1,
      title: 'Getting Started with STEM_XPERT',
      description: 'Learn the basics of robot control and programming',
      difficulty: 'Beginner',
      duration: '15 min',
      icon: <SmartToy />
    },
    {
      id: 2,
      title: 'Visual Programming with Blockly',
      description: 'Create your first robot program using visual blocks',
      difficulty: 'Beginner',
      duration: '20 min',
      icon: <Code />
    },
    {
      id: 3,
      title: 'Understanding Servo Motors',
      description: 'Learn how servo motors work and control robot movement',
      difficulty: 'Intermediate',
      duration: '25 min',
      icon: <Engineering />
    },
    {
      id: 4,
      title: 'Voice Recognition and AI',
      description: 'Explore artificial intelligence and voice commands',
      difficulty: 'Advanced',
      duration: '30 min',
      icon: <Psychology />
    }
  ];
  
  const challenges = [
    {
      id: 1,
      title: 'Make the Robot Wave',
      description: 'Program the robot to wave its arm',
      points: 100
    },
    {
      id: 2,
      title: 'Create a Dance Sequence',
      description: 'Combine multiple movements into a dance',
      points: 200
    },
    {
      id: 3,
      title: 'Voice-Controlled Movement',
      description: 'Make the robot respond to voice commands',
      points: 300
    },
    {
      id: 4,
      title: 'Face Following Robot',
      description: 'Program the robot to track and follow faces',
      points: 500
    }
  ];
  
  const getDifficultyColor = (difficulty) => {
    switch (difficulty) {
      case 'Beginner': return 'success';
      case 'Intermediate': return 'warning';
      case 'Advanced': return 'error';
      default: return 'default';
    }
  };
  
  const startTutorial = (tutorial) => {
    addNotification(`Starting tutorial: ${tutorial.title}`, 'info');
    // In a real implementation, this would navigate to the tutorial
  };
  
  const startChallenge = (challenge) => {
    addNotification(`Starting challenge: ${challenge.title}`, 'info');
    // In a real implementation, this would set up the challenge
  };
  
  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 600, color: 'white', mb: 3 }}>
        Education Center
      </Typography>
      
      <Grid container spacing={3}>
        {/* Tutorials */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <School sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">Tutorials</Typography>
              </Box>
              
              <List>
                {tutorials.map((tutorial) => (
                  <ListItem key={tutorial.id} sx={{ mb: 1 }}>
                    <ListItemIcon>
                      {tutorial.icon}
                    </ListItemIcon>
                    <ListItemText
                      primary={tutorial.title}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {tutorial.description}
                          </Typography>
                          <Box sx={{ mt: 1, display: 'flex', gap: 1 }}>
                            <Chip
                              label={tutorial.difficulty}
                              size="small"
                              color={getDifficultyColor(tutorial.difficulty)}
                            />
                            <Chip
                              label={tutorial.duration}
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                        </Box>
                      }
                    />
                    <Button
                      variant="contained"
                      size="small"
                      startIcon={<PlayArrow />}
                      onClick={() => startTutorial(tutorial)}
                    >
                      Start
                    </Button>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Challenges */}
        <Grid item xs={12} lg={6}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Psychology sx={{ mr: 1, color: 'secondary.main' }} />
                <Typography variant="h6">Challenges</Typography>
              </Box>
              
              <List>
                {challenges.map((challenge) => (
                  <ListItem key={challenge.id} sx={{ mb: 1 }}>
                    <ListItemText
                      primary={challenge.title}
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {challenge.description}
                          </Typography>
                          <Chip
                            label={`${challenge.points} points`}
                            size="small"
                            color="secondary"
                            sx={{ mt: 1 }}
                          />
                        </Box>
                      }
                    />
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => startChallenge(challenge)}
                    >
                      Try It
                    </Button>
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Learning Resources */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Learning Resources
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Programming Concepts
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Learn fundamental programming concepts like variables, loops, and functions through robot programming.
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        Robotics Fundamentals
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Understand how robots work, including sensors, actuators, and control systems.
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                
                <Grid item xs={12} md={4}>
                  <Card variant="outlined">
                    <CardContent>
                      <Typography variant="h6" gutterBottom>
                        AI and Machine Learning
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Explore artificial intelligence concepts through voice recognition and computer vision.
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default Education;
