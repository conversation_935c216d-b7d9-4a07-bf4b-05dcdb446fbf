body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Blockly workspace styling */
.blocklyToolboxDiv {
  background-color: #2d2d2d !important;
  border-right: 1px solid #444 !important;
}

.blocklyFlyout {
  background-color: #1a1a1a !important;
}

.blocklyMainBackground {
  stroke: none !important;
  fill: #1e1e1e !important;
}

.blocklyScrollbarBackground {
  fill: #333 !important;
}

.blocklyScrollbarHandle {
  fill: #666 !important;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}
