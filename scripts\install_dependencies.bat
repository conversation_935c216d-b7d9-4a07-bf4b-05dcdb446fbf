@echo off
REM STEM_XPERT Robot Dependencies Installation Script for Windows
REM This script installs all required Python and Node.js dependencies

setlocal enabledelayedexpansion

echo [%date% %time%] Installing STEM_XPERT Robot dependencies...

REM Get script directory
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..

echo Project directory: %PROJECT_DIR%

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python is not installed or not in PATH.
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo [WARNING] Node.js is not installed.
    echo Please install Node.js from https://nodejs.org for frontend development.
)

REM Create virtual environment
echo Creating Python virtual environment...
cd /d "%PROJECT_DIR%"
python -m venv venv
call venv\Scripts\activate.bat

REM Upgrade pip
echo Upgrading pip...
python -m pip install --upgrade pip setuptools wheel

REM Install Python dependencies
echo Installing Python dependencies...
pip install -r requirements.txt

REM Install Node.js dependencies for frontend
if exist "%PROJECT_DIR%\frontend\package.json" (
    echo Installing Node.js dependencies...
    cd /d "%PROJECT_DIR%\frontend"
    npm install
    
    REM Build frontend for production
    echo Building frontend for production...
    npm run build
    
    echo Frontend built successfully
)

REM Create necessary directories
echo Creating necessary directories...
if not exist "%PROJECT_DIR%\logs" mkdir "%PROJECT_DIR%\logs"
if not exist "%PROJECT_DIR%\models" mkdir "%PROJECT_DIR%\models"
if not exist "%PROJECT_DIR%\data" mkdir "%PROJECT_DIR%\data"
if not exist "%PROJECT_DIR%\config" mkdir "%PROJECT_DIR%\config"

REM Set up AI models directories
echo Setting up AI models...
if not exist "%PROJECT_DIR%\models\face_recognition" mkdir "%PROJECT_DIR%\models\face_recognition"
if not exist "%PROJECT_DIR%\models\gesture_recognition" mkdir "%PROJECT_DIR%\models\gesture_recognition"
if not exist "%PROJECT_DIR%\models\object_detection" mkdir "%PROJECT_DIR%\models\object_detection"

echo # Face recognition models go here > "%PROJECT_DIR%\models\face_recognition\README.md"
echo # Gesture recognition models go here > "%PROJECT_DIR%\models\gesture_recognition\README.md"
echo # Object detection models go here > "%PROJECT_DIR%\models\object_detection\README.md"

REM Test installation
echo Testing installation...
call "%PROJECT_DIR%\venv\Scripts\activate.bat"

python -c "import fastapi, uvicorn, cv2, numpy, pydantic; print('Core dependencies imported successfully')" 2>nul
if errorlevel 1 (
    echo [ERROR] Some dependencies failed to import
    pause
    exit /b 1
)

echo.
echo ========================================
echo STEM_XPERT Robot Installation Summary
echo ========================================
echo.
echo [SUCCESS] System dependencies checked
echo [SUCCESS] Python virtual environment created
echo [SUCCESS] Python dependencies installed
echo [SUCCESS] Frontend dependencies installed
echo [SUCCESS] Frontend built for production
echo [SUCCESS] Directory structure created
echo.
echo Project structure:
echo    %PROJECT_DIR%\
echo    ├── backend\          # Python backend code
echo    ├── frontend\         # React frontend code
echo    ├── config\           # Configuration files
echo    ├── scripts\          # Utility scripts
echo    ├── models\           # AI model files
echo    ├── logs\             # Log files
echo    └── venv\             # Python virtual environment
echo.
echo Next steps:
echo    1. Review configuration: config\robot_config.yaml
echo    2. Start the robot: scripts\start_robot.bat
echo    3. Open web interface: http://localhost:8000
echo.
echo For more information, see README.md
echo.

pause
