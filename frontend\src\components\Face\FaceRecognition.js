import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  Switch,
  FormControlLabel,
  Paper,
  List,
  ListItem,
  ListItemText,
  Chip
} from '@mui/material';
import {
  Face,
  CameraAlt,
  TrackChanges
} from '@mui/icons-material';

function FaceRecognition({ sendRobotCommand, addNotification }) {
  const [faceRecognitionActive, setFaceRecognitionActive] = useState(false);
  const [faceTrackingActive, setFaceTrackingActive] = useState(false);
  const [detectedFaces, setDetectedFaces] = useState([]);
  
  const handleToggleFaceRecognition = async () => {
    const newState = !faceRecognitionActive;
    setFaceRecognitionActive(newState);
    
    await sendRobotCommand({
      type: newState ? 'face_start' : 'face_stop'
    });
    
    addNotification(
      `Face recognition ${newState ? 'started' : 'stopped'}`,
      'info'
    );
  };
  
  const handleToggleFaceTracking = async () => {
    const newState = !faceTrackingActive;
    setFaceTrackingActive(newState);
    
    await sendRobotCommand({
      type: 'face_track',
      params: { enabled: newState }
    });
    
    addNotification(
      `Face tracking ${newState ? 'enabled' : 'disabled'}`,
      'info'
    );
  };
  
  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 600, color: 'white', mb: 3 }}>
        Face Recognition
      </Typography>
      
      <Grid container spacing={3}>
        {/* Controls */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Face Recognition Controls
              </Typography>
              
              <Box sx={{ mb: 3 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={faceRecognitionActive}
                      onChange={handleToggleFaceRecognition}
                    />
                  }
                  label="Face Recognition"
                />
              </Box>
              
              <Box sx={{ mb: 3 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={faceTrackingActive}
                      onChange={handleToggleFaceTracking}
                      disabled={!faceRecognitionActive}
                    />
                  }
                  label="Face Tracking"
                />
              </Box>
              
              <Typography variant="body2" color="text.secondary">
                Face tracking will automatically move the robot's head to follow detected faces.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Camera Feed Placeholder */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Camera Feed
              </Typography>
              
              <Paper
                sx={{
                  height: 300,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  bgcolor: 'grey.900',
                  color: 'white'
                }}
              >
                <Box sx={{ textAlign: 'center' }}>
                  <CameraAlt sx={{ fontSize: 64, mb: 2 }} />
                  <Typography variant="h6">
                    Camera Feed
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {faceRecognitionActive ? 'Active' : 'Inactive'}
                  </Typography>
                </Box>
              </Paper>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Detected Faces */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Detected Faces
              </Typography>
              
              <List>
                {detectedFaces.length === 0 ? (
                  <ListItem>
                    <ListItemText
                      primary="No faces detected"
                      secondary="Enable face recognition to start detecting faces"
                    />
                  </ListItem>
                ) : (
                  detectedFaces.map((face, index) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={face.name || 'Unknown Person'}
                        secondary={`Confidence: ${Math.round(face.confidence * 100)}%`}
                      />
                      <Chip
                        icon={<Face />}
                        label={face.name ? 'Known' : 'Unknown'}
                        color={face.name ? 'success' : 'default'}
                      />
                    </ListItem>
                  ))
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default FaceRecognition;
