# STEM_XPERT Robot Configuration
# Hardware and software settings for the educational robot

hardware:
  # Servo motor configurations
  servos:
    neck:
      pin: 0
      min_pulse: 500
      max_pulse: 2500
      min_angle: 30
      max_angle: 150
      default_angle: 90
      speed: 1.0
    
    left_shoulder:
      pin: 1
      min_pulse: 500
      max_pulse: 2500
      min_angle: 0
      max_angle: 180
      default_angle: 90
      speed: 1.0
    
    left_elbow:
      pin: 2
      min_pulse: 500
      max_pulse: 2500
      min_angle: 0
      max_angle: 180
      default_angle: 90
      speed: 1.0
    
    right_shoulder:
      pin: 3
      min_pulse: 500
      max_pulse: 2500
      min_angle: 0
      max_angle: 180
      default_angle: 90
      speed: 1.0
    
    right_elbow:
      pin: 4
      min_pulse: 500
      max_pulse: 2500
      min_angle: 0
      max_angle: 180
      default_angle: 90
      speed: 1.0
  
  # DC motor configurations for wheels
  motors:
    left_wheel:
      pin_a: 5
      pin_b: 6
      enable_pin: 7
      encoder_pin_a: 11
      encoder_pin_b: 12
      max_speed: 1.0
    
    right_wheel:
      pin_a: 8
      pin_b: 9
      enable_pin: 10
      encoder_pin_a: 13
      encoder_pin_b: 14
      max_speed: 1.0
  
  # Sensor configurations
  camera_index: 0
  microphone_index: 0
  speaker_pin: 18
  
  # I2C devices
  i2c_bus: 1
  mpu6050_address: 0x68
  
  # GPIO pins
  led_pin: 25
  button_pin: 24
  ultrasonic_trigger: 23
  ultrasonic_echo: 22

# AI and machine learning settings
ai:
  # Face recognition
  face_recognition_tolerance: 0.6
  face_detection_model: "hog"  # or "cnn" for better accuracy but slower
  
  # Voice recognition
  voice_recognition_timeout: 5.0
  voice_recognition_phrase_timeout: 0.3
  
  # Text-to-speech
  tts_rate: 150
  tts_volume: 0.9
  tts_voice: "english"
  
  # Computer vision
  cv_frame_width: 640
  cv_frame_height: 480
  cv_fps: 30
  
  # Model paths
  face_model_path: "models/face_recognition"
  gesture_model_path: "models/gesture_recognition"
  object_model_path: "models/object_detection"

# Network and communication
network:
  api_host: "0.0.0.0"
  api_port: 8000
  websocket_port: 8001
  mobile_app_port: 3000
  cors_origins: ["*"]

# Robot behavior settings
robot:
  # Movement parameters
  max_linear_speed: 0.5  # m/s
  max_angular_speed: 1.0  # rad/s
  acceleration_limit: 0.2  # m/s²
  
  # Servo movement
  servo_update_rate: 50.0  # Hz
  movement_smoothing: true
  
  # Behavior settings
  idle_timeout: 300.0  # seconds
  auto_sleep: true
  
  # Safety limits
  collision_distance: 0.2  # meters
  emergency_stop_enabled: true

# Educational settings
education:
  # Difficulty levels
  beginner_mode: true
  show_hints: true
  
  # Programming
  blockly_enabled: true
  python_enabled: false  # Advanced feature
  
  # Tutorials
  auto_start_tutorial: true
  tutorial_voice_guidance: true
