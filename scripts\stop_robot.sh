#!/bin/bash

# STEM_XPERT Robot Stop Script
# This script safely stops all robot services

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

log "Stopping STEM_XPERT Robot..."

# Function to stop process by PID file
stop_process() {
    local service_name=$1
    local pid_file="$PROJECT_DIR/logs/${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log "Stopping $service_name (PID: $pid)..."
            kill -TERM "$pid" 2>/dev/null || true
            
            # Wait for graceful shutdown
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # Force kill if still running
            if kill -0 "$pid" 2>/dev/null; then
                warning "Force killing $service_name..."
                kill -KILL "$pid" 2>/dev/null || true
            fi
            
            success "$service_name stopped"
        else
            warning "$service_name was not running"
        fi
        rm -f "$pid_file"
    else
        log "No PID file found for $service_name"
    fi
}

# Stop backend server
stop_process "backend"

# Stop frontend development server (if running)
stop_process "frontend"

# Kill any remaining Python processes related to the robot
log "Cleaning up remaining processes..."
pkill -f "python.*main.py" 2>/dev/null || true
pkill -f "uvicorn.*main:app" 2>/dev/null || true

# Kill any Node.js processes (frontend dev server)
pkill -f "node.*react-scripts" 2>/dev/null || true

# Send emergency stop to robot (if API is still accessible)
log "Sending emergency stop command..."
curl -s -X POST http://localhost:8000/api/v1/stop 2>/dev/null || true

# Clean up GPIO resources (if on Raspberry Pi)
if [ -e /dev/gpiomem ]; then
    log "Cleaning up GPIO resources..."
    # This would typically be handled by the Python cleanup code
    # but we can ensure it's done here as well
fi

# Clean up temporary files
log "Cleaning up temporary files..."
rm -f "$PROJECT_DIR/logs/*.pid"

# Archive logs with timestamp
if [ -f "$PROJECT_DIR/logs/robot.log" ]; then
    timestamp=$(date +"%Y%m%d_%H%M%S")
    mv "$PROJECT_DIR/logs/robot.log" "$PROJECT_DIR/logs/robot_${timestamp}.log" 2>/dev/null || true
fi

if [ -f "$PROJECT_DIR/logs/frontend.log" ]; then
    timestamp=$(date +"%Y%m%d_%H%M%S")
    mv "$PROJECT_DIR/logs/frontend.log" "$PROJECT_DIR/logs/frontend_${timestamp}.log" 2>/dev/null || true
fi

success "STEM_XPERT Robot stopped successfully"

# Display final status
echo ""
echo "🤖 Robot Status: STOPPED"
echo ""
echo "📋 Archived Logs:"
ls -la "$PROJECT_DIR/logs/"*.log 2>/dev/null | tail -5 || echo "   No log files found"
echo ""
echo "🚀 To start the robot again:"
echo "   Run: $SCRIPT_DIR/start_robot.sh"
echo ""
