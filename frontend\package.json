{"name": "stem-xpert-robot-frontend", "version": "1.0.0", "description": "STEM_XPERT Robot Control Interface", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.5.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "react-router-dom": "^6.8.1", "web-vitals": "^2.1.4", "blockly": "^10.2.2", "axios": "^1.6.2", "socket.io-client": "^4.7.4", "@mui/material": "^5.15.0", "@mui/icons-material": "^5.15.0", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "recharts": "^2.8.0", "react-joystick-component": "^6.0.0", "react-speech-recognition": "^3.10.0", "react-webcam": "^7.1.1", "three": "^0.158.0", "@react-three/fiber": "^8.15.11", "@react-three/drei": "^9.88.13", "framer-motion": "^10.16.16"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "typescript": "^4.9.5"}, "proxy": "http://localhost:8000"}