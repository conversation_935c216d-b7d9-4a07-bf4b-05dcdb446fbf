import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  Button,
  TextField,
  Grid,
  List,
  ListItem,
  ListItemText,
  Chip,
  IconButton
} from '@mui/material';
import {
  Mic,
  MicOff,
  VolumeUp,
  Clear
} from '@mui/icons-material';

function VoiceControl({ sendRobotCommand, addNotification }) {
  const [isListening, setIsListening] = useState(false);
  const [speechText, setSpeechText] = useState('');
  const [voiceCommands, setVoiceCommands] = useState([]);
  
  const quickPhrases = [
    'Hello, I am STEM_XPERT robot!',
    'Nice to meet you!',
    'I can help you learn programming.',
    'Let me show you what I can do.',
    'Thank you for using STEM_XPERT!'
  ];
  
  const handleStartListening = async () => {
    setIsListening(true);
    await sendRobotCommand({
      type: 'voice_start'
    });
    addNotification('Voice recognition started', 'info');
  };
  
  const handleStopListening = async () => {
    setIsListening(false);
    await sendRobotCommand({
      type: 'voice_stop'
    });
    addNotification('Voice recognition stopped', 'info');
  };
  
  const handleSpeak = async (text = speechText) => {
    if (!text.trim()) {
      addNotification('Please enter text to speak', 'warning');
      return;
    }
    
    await sendRobotCommand({
      type: 'speak',
      params: { text }
    });
    
    addNotification(`Speaking: "${text}"`, 'info');
  };
  
  const clearCommands = () => {
    setVoiceCommands([]);
  };
  
  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 600, color: 'white', mb: 3 }}>
        Voice Control
      </Typography>
      
      <Grid container spacing={3}>
        {/* Voice Recognition */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Voice Recognition
              </Typography>
              
              <Box sx={{ textAlign: 'center', mb: 3 }}>
                {isListening ? (
                  <Button
                    variant="contained"
                    color="error"
                    size="large"
                    startIcon={<MicOff />}
                    onClick={handleStopListening}
                    sx={{ minWidth: 200 }}
                  >
                    Stop Listening
                  </Button>
                ) : (
                  <Button
                    variant="contained"
                    color="primary"
                    size="large"
                    startIcon={<Mic />}
                    onClick={handleStartListening}
                    sx={{ minWidth: 200 }}
                  >
                    Start Listening
                  </Button>
                )}
              </Box>
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="h6">
                  Voice Commands
                </Typography>
                <IconButton onClick={clearCommands} size="small">
                  <Clear />
                </IconButton>
              </Box>
              
              <List sx={{ maxHeight: 300, overflow: 'auto' }}>
                {voiceCommands.length === 0 ? (
                  <ListItem>
                    <ListItemText
                      primary="No voice commands yet"
                      secondary="Start listening to capture voice commands"
                    />
                  </ListItem>
                ) : (
                  voiceCommands.map((command, index) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={command.text}
                        secondary={new Date(command.timestamp).toLocaleTimeString()}
                      />
                      <Chip
                        label={`${Math.round(command.confidence * 100)}%`}
                        size="small"
                        color="primary"
                      />
                    </ListItem>
                  ))
                )}
              </List>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Text-to-Speech */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Text-to-Speech
              </Typography>
              
              <TextField
                fullWidth
                multiline
                rows={4}
                label="Text to speak"
                value={speechText}
                onChange={(e) => setSpeechText(e.target.value)}
                sx={{ mb: 2 }}
              />
              
              <Button
                fullWidth
                variant="contained"
                startIcon={<VolumeUp />}
                onClick={() => handleSpeak()}
                sx={{ mb: 3 }}
              >
                Speak Text
              </Button>
              
              <Typography variant="h6" gutterBottom>
                Quick Phrases
              </Typography>
              
              <Grid container spacing={1}>
                {quickPhrases.map((phrase, index) => (
                  <Grid item xs={12} key={index}>
                    <Button
                      fullWidth
                      variant="outlined"
                      size="small"
                      onClick={() => handleSpeak(phrase)}
                      sx={{ justifyContent: 'flex-start', textAlign: 'left' }}
                    >
                      {phrase}
                    </Button>
                  </Grid>
                ))}
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default VoiceControl;
