<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="STEM_XPERT Robot - Educational Humanoid Robot Control Interface"
    />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Material-UI Roboto Font -->
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700&display=swap"
    />
    
    <!-- Material-UI Icons -->
    <link
      rel="stylesheet"
      href="https://fonts.googleapis.com/icon?family=Material+Icons"
    />
    
    <title>STEM_XPERT Robot Control</title>
    
    <style>
      body {
        margin: 0;
        font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Oxygen',
          'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
          sans-serif;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }
      
      code {
        font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
          monospace;
      }
      
      /* Loading animation */
      .loading-container {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100vh;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      /* Robot status indicator */
      .robot-status {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1000;
        padding: 10px 15px;
        border-radius: 20px;
        color: white;
        font-weight: bold;
        font-size: 14px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
      }
      
      .robot-status.connected {
        background-color: #4caf50;
      }
      
      .robot-status.disconnected {
        background-color: #f44336;
      }
      
      .robot-status.connecting {
        background-color: #ff9800;
      }
      
      /* Blockly workspace styling */
      .blockly-workspace {
        height: 100%;
        width: 100%;
      }
      
      /* Custom scrollbar */
      ::-webkit-scrollbar {
        width: 8px;
      }
      
      ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 4px;
      }
      
      ::-webkit-scrollbar-thumb:hover {
        background: #555;
      }
      
      /* Responsive design */
      @media (max-width: 768px) {
        .robot-status {
          top: 10px;
          right: 10px;
          font-size: 12px;
          padding: 8px 12px;
        }
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    
    <!-- Loading screen -->
    <div id="loading-screen" class="loading-container">
      <div>
        <div class="loading-spinner"></div>
        <p style="color: white; margin-top: 20px; text-align: center;">
          Loading STEM_XPERT Robot Control...
        </p>
      </div>
    </div>
    
    <!-- Main app container -->
    <div id="root"></div>
    
    <!-- Robot status indicator -->
    <div id="robot-status" class="robot-status disconnected">
      Robot: Disconnected
    </div>
    
    <script>
      // Hide loading screen when React app loads
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.style.display = 'none';
          }
        }, 1000);
      });
      
      // Update robot status indicator
      window.updateRobotStatus = function(status) {
        const statusElement = document.getElementById('robot-status');
        if (statusElement) {
          statusElement.className = `robot-status ${status}`;
          statusElement.textContent = `Robot: ${status.charAt(0).toUpperCase() + status.slice(1)}`;
        }
      };
    </script>
  </body>
</html>
