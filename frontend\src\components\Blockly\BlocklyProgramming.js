import React, { useEffect, useRef, useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Chip
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  Save as SaveIcon,
  FolderOpen as LoadIcon,
  Delete as DeleteIcon,
  Code as CodeIcon
} from '@mui/icons-material';
import * as Blockly from 'blockly/core';
import 'blockly/blocks';
import 'blockly/javascript';

// Custom blocks for robot control
const robotBlocks = {
  // Movement blocks
  robot_move: {
    init: function() {
      this.appendDummyInput()
        .appendField("move robot")
        .appendField(new Blockly.FieldDropdown([
          ["forward", "forward"],
          ["backward", "backward"],
          ["left", "left"],
          ["right", "right"],
          ["stop", "stop"]
        ]), "DIRECTION");
      this.appendValueInput("DURATION")
        .setCheck("Number")
        .appendField("for");
      this.appendDummyInput()
        .appendField("seconds");
      this.setPreviousStatement(true, null);
      this.setNextStatement(true, null);
      this.setColour(160);
      this.setTooltip("Move the robot in a direction for a specified time");
    }
  },
  
  // Servo control blocks
  robot_servo: {
    init: function() {
      this.appendDummyInput()
        .appendField("move")
        .appendField(new Blockly.FieldDropdown([
          ["neck", "neck"],
          ["left shoulder", "left_shoulder"],
          ["left elbow", "left_elbow"],
          ["right shoulder", "right_shoulder"],
          ["right elbow", "right_elbow"]
        ]), "SERVO");
      this.appendValueInput("ANGLE")
        .setCheck("Number")
        .appendField("to");
      this.appendDummyInput()
        .appendField("degrees");
      this.setPreviousStatement(true, null);
      this.setNextStatement(true, null);
      this.setColour(200);
      this.setTooltip("Move a servo to a specific angle");
    }
  },
  
  // Speech blocks
  robot_speak: {
    init: function() {
      this.appendValueInput("TEXT")
        .setCheck("String")
        .appendField("say");
      this.setPreviousStatement(true, null);
      this.setNextStatement(true, null);
      this.setColour(260);
      this.setTooltip("Make the robot speak text");
    }
  },
  
  // Dance blocks
  robot_dance: {
    init: function() {
      this.appendDummyInput()
        .appendField("perform dance")
        .appendField(new Blockly.FieldDropdown([
          ["wave", "wave"],
          ["nod", "nod"],
          ["celebrate", "celebrate"]
        ]), "DANCE");
      this.setPreviousStatement(true, null);
      this.setNextStatement(true, null);
      this.setColour(300);
      this.setTooltip("Perform a predefined dance sequence");
    }
  },
  
  // Wait block
  robot_wait: {
    init: function() {
      this.appendValueInput("DURATION")
        .setCheck("Number")
        .appendField("wait");
      this.appendDummyInput()
        .appendField("seconds");
      this.setPreviousStatement(true, null);
      this.setNextStatement(true, null);
      this.setColour(120);
      this.setTooltip("Wait for a specified number of seconds");
    }
  }
};

// JavaScript code generators
const robotGenerators = {
  robot_move: function(block) {
    const direction = block.getFieldValue('DIRECTION');
    const duration = Blockly.JavaScript.valueToCode(block, 'DURATION', Blockly.JavaScript.ORDER_ATOMIC) || '1';
    return `robot.move('${direction}', ${duration});\n`;
  },
  
  robot_servo: function(block) {
    const servo = block.getFieldValue('SERVO');
    const angle = Blockly.JavaScript.valueToCode(block, 'ANGLE', Blockly.JavaScript.ORDER_ATOMIC) || '90';
    return `robot.servo('${servo}', ${angle});\n`;
  },
  
  robot_speak: function(block) {
    const text = Blockly.JavaScript.valueToCode(block, 'TEXT', Blockly.JavaScript.ORDER_ATOMIC) || '""';
    return `robot.speak(${text});\n`;
  },
  
  robot_dance: function(block) {
    const dance = block.getFieldValue('DANCE');
    return `robot.dance('${dance}');\n`;
  },
  
  robot_wait: function(block) {
    const duration = Blockly.JavaScript.valueToCode(block, 'DURATION', Blockly.JavaScript.ORDER_ATOMIC) || '1';
    return `robot.wait(${duration});\n`;
  }
};

function BlocklyProgramming({ wsService, addNotification }) {
  const blocklyDiv = useRef(null);
  const [workspace, setWorkspace] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [saveDialogOpen, setSaveDialogOpen] = useState(false);
  const [loadDialogOpen, setLoadDialogOpen] = useState(false);
  const [programName, setProgramName] = useState('');
  const [savedPrograms, setSavedPrograms] = useState([]);
  const [generatedCode, setGeneratedCode] = useState('');
  
  useEffect(() => {
    initializeBlockly();
    loadSavedPrograms();
    
    return () => {
      if (workspace) {
        workspace.dispose();
      }
    };
  }, []);
  
  const initializeBlockly = () => {
    // Register custom blocks
    Object.entries(robotBlocks).forEach(([name, block]) => {
      Blockly.Blocks[name] = block;
    });
    
    // Register code generators
    Object.entries(robotGenerators).forEach(([name, generator]) => {
      Blockly.JavaScript[name] = generator;
    });
    
    // Create toolbox
    const toolbox = {
      kind: 'categoryToolbox',
      contents: [
        {
          kind: 'category',
          name: 'Movement',
          colour: 160,
          contents: [
            { kind: 'block', type: 'robot_move' },
            { kind: 'block', type: 'robot_wait' }
          ]
        },
        {
          kind: 'category',
          name: 'Servos',
          colour: 200,
          contents: [
            { kind: 'block', type: 'robot_servo' }
          ]
        },
        {
          kind: 'category',
          name: 'Speech',
          colour: 260,
          contents: [
            { kind: 'block', type: 'robot_speak' }
          ]
        },
        {
          kind: 'category',
          name: 'Dance',
          colour: 300,
          contents: [
            { kind: 'block', type: 'robot_dance' }
          ]
        },
        {
          kind: 'category',
          name: 'Logic',
          colour: 210,
          contents: [
            { kind: 'block', type: 'controls_if' },
            { kind: 'block', type: 'controls_repeat_ext' },
            { kind: 'block', type: 'logic_compare' },
            { kind: 'block', type: 'logic_operation' },
            { kind: 'block', type: 'logic_boolean' }
          ]
        },
        {
          kind: 'category',
          name: 'Math',
          colour: 230,
          contents: [
            { kind: 'block', type: 'math_number' },
            { kind: 'block', type: 'math_arithmetic' },
            { kind: 'block', type: 'math_random_int' }
          ]
        },
        {
          kind: 'category',
          name: 'Text',
          colour: 160,
          contents: [
            { kind: 'block', type: 'text' },
            { kind: 'block', type: 'text_join' }
          ]
        }
      ]
    };
    
    // Initialize workspace
    const ws = Blockly.inject(blocklyDiv.current, {
      toolbox: toolbox,
      grid: {
        spacing: 20,
        length: 3,
        colour: '#ccc',
        snap: true
      },
      zoom: {
        controls: true,
        wheel: true,
        startScale: 1.0,
        maxScale: 3,
        minScale: 0.3,
        scaleSpeed: 1.2
      },
      trashcan: true,
      theme: Blockly.Themes.Dark
    });
    
    // Add change listener to generate code
    ws.addChangeListener(() => {
      const code = Blockly.JavaScript.workspaceToCode(ws);
      setGeneratedCode(code);
    });
    
    setWorkspace(ws);
  };
  
  const loadSavedPrograms = () => {
    const saved = localStorage.getItem('robotPrograms');
    if (saved) {
      setSavedPrograms(JSON.parse(saved));
    }
  };
  
  const saveProgram = () => {
    if (!programName.trim()) {
      addNotification('Please enter a program name', 'warning');
      return;
    }
    
    const xml = Blockly.Xml.workspaceToDom(workspace);
    const xmlText = Blockly.Xml.domToText(xml);
    
    const program = {
      id: Date.now(),
      name: programName,
      xml: xmlText,
      code: generatedCode,
      created: new Date().toISOString()
    };
    
    const updated = [...savedPrograms, program];
    setSavedPrograms(updated);
    localStorage.setItem('robotPrograms', JSON.stringify(updated));
    
    setProgramName('');
    setSaveDialogOpen(false);
    addNotification(`Program "${program.name}" saved`, 'success');
  };
  
  const loadProgram = (program) => {
    try {
      workspace.clear();
      const xml = Blockly.Xml.textToDom(program.xml);
      Blockly.Xml.domToWorkspace(xml, workspace);
      setLoadDialogOpen(false);
      addNotification(`Program "${program.name}" loaded`, 'success');
    } catch (error) {
      addNotification('Failed to load program', 'error');
    }
  };
  
  const deleteProgram = (programId) => {
    const updated = savedPrograms.filter(p => p.id !== programId);
    setSavedPrograms(updated);
    localStorage.setItem('robotPrograms', JSON.stringify(updated));
    addNotification('Program deleted', 'success');
  };
  
  const runProgram = async () => {
    if (!generatedCode.trim()) {
      addNotification('No program to run', 'warning');
      return;
    }
    
    setIsRunning(true);
    
    try {
      // Convert generated code to robot commands
      const commands = parseGeneratedCode(generatedCode);
      
      // Send to robot via WebSocket
      wsService.executeBlocklyProgram(JSON.stringify(commands), programName || 'Untitled');
      
      addNotification('Program started', 'success');
    } catch (error) {
      addNotification('Failed to run program', 'error');
    } finally {
      setIsRunning(false);
    }
  };
  
  const parseGeneratedCode = (code) => {
    // Simple parser to convert generated JavaScript to robot commands
    const commands = [];
    const lines = code.split('\n').filter(line => line.trim());
    
    lines.forEach(line => {
      if (line.includes('robot.move(')) {
        const match = line.match(/robot\.move\('(\w+)', (\d+(?:\.\d+)?)\)/);
        if (match) {
          const [, direction, duration] = match;
          commands.push({
            type: 'move',
            params: { direction, duration: parseFloat(duration) }
          });
        }
      } else if (line.includes('robot.servo(')) {
        const match = line.match(/robot\.servo\('(\w+)', (\d+(?:\.\d+)?)\)/);
        if (match) {
          const [, servo, angle] = match;
          commands.push({
            type: 'servo',
            params: { servo, angle: parseFloat(angle) }
          });
        }
      } else if (line.includes('robot.speak(')) {
        const match = line.match(/robot\.speak\("([^"]*)"\)/);
        if (match) {
          const [, text] = match;
          commands.push({
            type: 'speak',
            params: { text }
          });
        }
      } else if (line.includes('robot.dance(')) {
        const match = line.match(/robot\.dance\('(\w+)'\)/);
        if (match) {
          const [, dance] = match;
          commands.push({
            type: 'dance',
            params: { dance }
          });
        }
      } else if (line.includes('robot.wait(')) {
        const match = line.match(/robot\.wait\((\d+(?:\.\d+)?)\)/);
        if (match) {
          const [, duration] = match;
          commands.push({
            type: 'delay',
            params: { duration: parseFloat(duration) }
          });
        }
      }
    });
    
    return commands;
  };
  
  const stopProgram = () => {
    setIsRunning(false);
    // Send stop command to robot
    wsService.sendRobotCommand({ type: 'stop' });
    addNotification('Program stopped', 'info');
  };
  
  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, color: 'white' }}>
          Visual Programming
        </Typography>
        
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="contained"
            startIcon={<SaveIcon />}
            onClick={() => setSaveDialogOpen(true)}
            sx={{ color: 'white' }}
          >
            Save
          </Button>
          
          <Button
            variant="outlined"
            startIcon={<LoadIcon />}
            onClick={() => setLoadDialogOpen(true)}
            sx={{ color: 'white', borderColor: 'white' }}
          >
            Load
          </Button>
          
          {isRunning ? (
            <Button
              variant="contained"
              color="error"
              startIcon={<StopIcon />}
              onClick={stopProgram}
            >
              Stop
            </Button>
          ) : (
            <Button
              variant="contained"
              color="success"
              startIcon={<PlayIcon />}
              onClick={runProgram}
              disabled={!generatedCode.trim()}
            >
              Run
            </Button>
          )}
        </Box>
      </Box>
      
      <Grid container spacing={3}>
        {/* Blockly Workspace */}
        <Grid item xs={12} lg={8}>
          <Card sx={{ height: 600 }}>
            <CardContent sx={{ height: '100%', p: 0 }}>
              <div
                ref={blocklyDiv}
                style={{ height: '100%', width: '100%' }}
              />
            </CardContent>
          </Card>
        </Grid>
        
        {/* Generated Code */}
        <Grid item xs={12} lg={4}>
          <Card sx={{ height: 600 }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CodeIcon sx={{ mr: 1 }} />
                <Typography variant="h6">Generated Code</Typography>
              </Box>
              
              <Box
                sx={{
                  height: 500,
                  overflow: 'auto',
                  backgroundColor: '#1e1e1e',
                  color: '#d4d4d4',
                  p: 2,
                  borderRadius: 1,
                  fontFamily: 'monospace',
                  fontSize: '0.875rem',
                  lineHeight: 1.5
                }}
              >
                <pre>{generatedCode || '// No code generated yet'}</pre>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
      
      {/* Save Dialog */}
      <Dialog open={saveDialogOpen} onClose={() => setSaveDialogOpen(false)}>
        <DialogTitle>Save Program</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Program Name"
            fullWidth
            variant="outlined"
            value={programName}
            onChange={(e) => setProgramName(e.target.value)}
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSaveDialogOpen(false)}>Cancel</Button>
          <Button onClick={saveProgram} variant="contained">Save</Button>
        </DialogActions>
      </Dialog>
      
      {/* Load Dialog */}
      <Dialog open={loadDialogOpen} onClose={() => setLoadDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Load Program</DialogTitle>
        <DialogContent>
          <List>
            {savedPrograms.map((program) => (
              <ListItem key={program.id}>
                <ListItemText
                  primary={program.name}
                  secondary={`Created: ${new Date(program.created).toLocaleString()}`}
                />
                <ListItemSecondaryAction>
                  <IconButton
                    edge="end"
                    onClick={() => loadProgram(program)}
                    sx={{ mr: 1 }}
                  >
                    <LoadIcon />
                  </IconButton>
                  <IconButton
                    edge="end"
                    onClick={() => deleteProgram(program.id)}
                    color="error"
                  >
                    <DeleteIcon />
                  </IconButton>
                </ListItemSecondaryAction>
              </ListItem>
            ))}
          </List>
          
          {savedPrograms.length === 0 && (
            <Typography variant="body2" color="text.secondary" sx={{ textAlign: 'center', py: 4 }}>
              No saved programs
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setLoadDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
}

export default BlocklyProgramming;
