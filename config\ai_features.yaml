# STEM_XPERT Robot AI Features Configuration
# Alpha 1E Compatible AI Capabilities

# Speech Recognition & Text-to-Speech (Alpha 1E Compatible)
speech_recognition:
  # Multiple speech-to-text engines (like Alpha 1E)
  engines:
    primary: "google"  # Google Speech API (online, high accuracy)
    backup: "vosk"     # Vosk offline recognition (medium accuracy)
    advanced: "whisper" # OpenAI Whisper (very high accuracy)
  
  # Voice Activity Detection
  vad:
    enabled: true
    aggressiveness: 2  # 0-3, higher = more aggressive
    frame_duration: 30  # milliseconds
  
  # Recognition settings
  settings:
    sample_rate: 16000
    timeout: 5.0
    phrase_timeout: 0.3
    ambient_noise_adjustment: true
    confidence_threshold: 0.7
  
  # Language support
  languages:
    - "en-US"  # English (US)
    - "en-GB"  # English (UK)
    - "es-ES"  # Spanish
    - "fr-FR"  # French
    - "de-DE"  # German
    - "zh-CN"  # Chinese (Simplified)

# Text-to-Speech (Enhanced Alpha 1E Style)
text_to_speech:
  # TTS Engine settings
  engine: "pyttsx3"
  voice_type: "female"  # Alpha 1E uses female voice
  
  # Voice parameters
  settings:
    rate: 150          # Words per minute
    volume: 0.9        # 0.0 to 1.0
    pitch: 0.5         # Voice pitch
  
  # Emotional expressions
  emotions:
    happy: { rate: 160, pitch: 0.6 }
    sad: { rate: 120, pitch: 0.3 }
    excited: { rate: 180, pitch: 0.7 }
    calm: { rate: 140, pitch: 0.4 }
    surprised: { rate: 170, pitch: 0.8 }

# Natural Language Processing
nlp:
  # Intent Recognition (Alpha 1E Style)
  intent_classification:
    enabled: true
    confidence_threshold: 0.6
    
    # Supported intents
    intents:
      movement:
        keywords: ["move", "go", "walk", "forward", "backward", "left", "right", "stop", "turn"]
        patterns:
          - "move (forward|backward|left|right)"
          - "go (forward|backward|left|right)"
          - "turn (left|right)"
          - "stop"
      
      greeting:
        keywords: ["hello", "hi", "hey", "good morning", "good afternoon"]
        patterns:
          - "hello"
          - "hi there"
          - "good (morning|afternoon|evening)"
      
      dance:
        keywords: ["dance", "wave", "celebrate", "perform"]
        patterns:
          - "(dance|wave|celebrate)"
          - "perform (dance|wave)"
      
      question:
        keywords: ["what", "how", "why", "when", "where", "who"]
        patterns:
          - "what (is|are|can)"
          - "how (do|can|does)"
          - "why (is|are|do)"
      
      control:
        keywords: ["arm", "hand", "head", "neck", "servo"]
        patterns:
          - "move (arm|hand|head|neck)"
          - "raise (arm|hand)"
          - "turn (head|neck)"
      
      learning:
        keywords: ["teach", "learn", "explain", "show", "tutorial"]
        patterns:
          - "teach me about"
          - "explain (how|what|why)"
          - "show me"
  
  # Entity Extraction
  entity_extraction:
    enabled: true
    entities:
      - direction
      - duration
      - body_part
      - action
      - dance_type
      - emotion

# Computer Vision (Alpha 1E Compatible)
computer_vision:
  # Face Recognition
  face_recognition:
    enabled: true
    engines:
      - "opencv_haar"    # Fast detection
      - "mediapipe"      # Accurate detection
      - "face_recognition" # Recognition
    
    settings:
      detection_confidence: 0.5
      tracking_confidence: 0.5
      max_faces: 5
      recognition_tolerance: 0.6
    
    features:
      face_detection: true
      face_recognition: true
      face_tracking: true
      emotion_detection: true
      age_estimation: true
      gender_detection: true
  
  # Gesture Recognition
  gesture_recognition:
    enabled: true
    engine: "mediapipe"
    
    settings:
      max_hands: 2
      detection_confidence: 0.7
      tracking_confidence: 0.5
    
    gestures:
      - "wave"
      - "point"
      - "thumbs_up"
      - "peace_sign"
      - "stop"
      - "come_here"
  
  # Object Detection
  object_detection:
    enabled: true
    engine: "yolo"  # or "tensorflow"
    
    settings:
      confidence_threshold: 0.5
      nms_threshold: 0.4
      max_objects: 10
    
    classes:
      - "person"
      - "book"
      - "laptop"
      - "cell phone"
      - "cup"
      - "bottle"

# Conversational AI (Alpha 1E Style)
conversation:
  # Personality settings
  personality:
    name: "STEM_XPERT"
    character: "friendly_teacher"
    age: "young_adult"
    enthusiasm: 0.8
  
  # Response generation
  responses:
    greeting:
      - "Hello! I'm STEM_XPERT, your educational robot companion!"
      - "Hi there! Ready to learn about robotics and programming?"
      - "Greetings! I'm excited to help you explore STEM subjects!"
    
    introduction:
      - "I'm STEM_XPERT, an advanced educational robot with AI capabilities."
      - "I can help you learn programming, robotics, and computer science."
      - "I understand voice commands and can respond naturally to questions."
    
    capabilities:
      - "I can move around, recognize faces, understand speech, and teach programming."
      - "I support visual programming with Blockly and can demonstrate robotics concepts."
      - "I have computer vision, natural language processing, and educational features."
    
    encouragement:
      - "Great job! You're learning quickly!"
      - "That's an excellent question! Let me explain..."
      - "You're doing wonderfully! Keep exploring!"
  
  # Context awareness
  context:
    memory_length: 10  # Remember last 10 interactions
    topic_tracking: true
    user_preferences: true

# Educational AI Features
education:
  # Adaptive learning
  adaptive_learning:
    enabled: true
    difficulty_adjustment: true
    progress_tracking: true
    personalized_content: true
  
  # Teaching modes
  teaching_modes:
    beginner:
      pace: "slow"
      explanations: "detailed"
      examples: "many"
      encouragement: "frequent"
    
    intermediate:
      pace: "medium"
      explanations: "moderate"
      examples: "some"
      encouragement: "regular"
    
    advanced:
      pace: "fast"
      explanations: "brief"
      examples: "few"
      encouragement: "minimal"
  
  # Subject areas
  subjects:
    - "programming"
    - "robotics"
    - "electronics"
    - "mathematics"
    - "physics"
    - "computer_science"

# AI Model Paths
models:
  speech:
    vosk: "models/vosk-model-en-us-0.22"
    whisper: "models/whisper-base"
  
  vision:
    face_recognition: "models/face_recognition"
    gesture_recognition: "models/gesture_recognition"
    object_detection: "models/yolo"
  
  nlp:
    intent_classifier: "models/intent_classifier"
    entity_extractor: "models/entity_extractor"

# Performance Settings
performance:
  # Processing limits
  max_concurrent_requests: 5
  processing_timeout: 30.0
  
  # Resource management
  memory_limit: "2GB"
  cpu_usage_limit: 80
  
  # Optimization
  use_gpu: false  # Set to true if GPU available
  model_caching: true
  batch_processing: false
