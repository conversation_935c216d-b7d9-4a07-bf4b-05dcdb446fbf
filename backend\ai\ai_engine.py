"""
AI Engine - Handles all AI-related functionality
Voice recognition, face recognition, computer vision, and natural language processing
"""

import asyncio
import logging
import cv2
import numpy as np
import threading
import queue
from typing import Dict, Any, Optional, List, Tuple
from dataclasses import dataclass

try:
    import speech_recognition as sr
    import pyttsx3
    import face_recognition
    import mediapipe as mp
    HAS_AI_LIBS = True
except ImportError:
    HAS_AI_LIBS = False
    logging.warning("AI libraries not available - running in limited mode")

from core.config import Config

logger = logging.getLogger(__name__)

@dataclass
class FaceData:
    """Face detection data"""
    x: float
    y: float
    width: float
    height: float
    confidence: float
    name: Optional[str] = None

@dataclass
class VoiceCommand:
    """Voice command data"""
    text: str
    confidence: float
    language: str
    timestamp: float

class VoiceRecognitionEngine:
    """Voice recognition and speech synthesis"""
    
    def __init__(self, config: Config):
        self.config = config
        self.recognizer = None
        self.microphone = None
        self.tts_engine = None
        self.is_listening = False
        self.command_queue = queue.Queue()
        
        if HAS_AI_LIBS:
            self.recognizer = sr.Recognizer()
            self.microphone = sr.Microphone(device_index=config.hardware.microphone_index)
            
            # Initialize TTS
            self.tts_engine = pyttsx3.init()
            self.tts_engine.setProperty('rate', config.ai.tts_rate)
            self.tts_engine.setProperty('volume', config.ai.tts_volume)
    
    async def initialize(self):
        """Initialize voice recognition"""
        if not HAS_AI_LIBS:
            logger.warning("Voice recognition not available")
            return
        
        try:
            # Adjust for ambient noise
            with self.microphone as source:
                self.recognizer.adjust_for_ambient_noise(source)
            
            logger.info("Voice recognition initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize voice recognition: {e}")
    
    async def start_listening(self):
        """Start continuous voice recognition"""
        if not HAS_AI_LIBS or self.is_listening:
            return
        
        self.is_listening = True
        
        # Start listening in background thread
        def listen_continuously():
            while self.is_listening:
                try:
                    with self.microphone as source:
                        # Listen for audio with timeout
                        audio = self.recognizer.listen(
                            source, 
                            timeout=self.config.ai.voice_recognition_timeout,
                            phrase_time_limit=self.config.ai.voice_recognition_phrase_timeout
                        )
                    
                    # Recognize speech
                    try:
                        text = self.recognizer.recognize_google(audio)
                        command = VoiceCommand(
                            text=text.lower(),
                            confidence=1.0,  # Google API doesn't provide confidence
                            language="en",
                            timestamp=asyncio.get_event_loop().time()
                        )
                        self.command_queue.put(command)
                        logger.info(f"Voice command: {text}")
                        
                    except sr.UnknownValueError:
                        # Speech not understood
                        pass
                    except sr.RequestError as e:
                        logger.error(f"Voice recognition error: {e}")
                
                except sr.WaitTimeoutError:
                    # No speech detected
                    pass
                except Exception as e:
                    logger.error(f"Error in voice recognition: {e}")
        
        # Start listening thread
        self.listen_thread = threading.Thread(target=listen_continuously)
        self.listen_thread.daemon = True
        self.listen_thread.start()
        
        logger.info("Voice recognition started")
    
    async def stop_listening(self):
        """Stop voice recognition"""
        self.is_listening = False
        logger.info("Voice recognition stopped")
    
    async def speak_text(self, text: str, language: str = "en"):
        """Convert text to speech"""
        if not HAS_AI_LIBS:
            logger.info(f"TTS (simulated): {text}")
            return
        
        try:
            # Run TTS in thread to avoid blocking
            def speak():
                self.tts_engine.say(text)
                self.tts_engine.runAndWait()
            
            speak_thread = threading.Thread(target=speak)
            speak_thread.daemon = True
            speak_thread.start()
            
            logger.info(f"Speaking: {text}")
            
        except Exception as e:
            logger.error(f"TTS error: {e}")
    
    def get_voice_command(self) -> Optional[VoiceCommand]:
        """Get next voice command from queue"""
        try:
            return self.command_queue.get_nowait()
        except queue.Empty:
            return None

class FaceRecognitionEngine:
    """Face detection and recognition"""
    
    def __init__(self, config: Config):
        self.config = config
        self.known_faces = {}
        self.face_cascade = None
        self.is_active = False
        self.current_faces = []
        
        if HAS_AI_LIBS:
            # Load face cascade for detection
            cascade_path = cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            self.face_cascade = cv2.CascadeClassifier(cascade_path)
    
    async def initialize(self):
        """Initialize face recognition"""
        if not HAS_AI_LIBS:
            logger.warning("Face recognition not available")
            return
        
        try:
            # Load known faces from directory
            await self._load_known_faces()
            logger.info("Face recognition initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize face recognition: {e}")
    
    async def _load_known_faces(self):
        """Load known faces from storage"""
        # This would load face encodings from a database or files
        # For now, we'll use a placeholder
        self.known_faces = {}
        logger.info("Known faces loaded")
    
    async def add_known_face(self, name: str, image_path: str):
        """Add a new known face"""
        if not HAS_AI_LIBS:
            return
        
        try:
            # Load image and create encoding
            image = face_recognition.load_image_file(image_path)
            encodings = face_recognition.face_encodings(image)
            
            if encodings:
                self.known_faces[name] = encodings[0]
                logger.info(f"Added known face: {name}")
            else:
                logger.warning(f"No face found in image: {image_path}")
                
        except Exception as e:
            logger.error(f"Error adding known face: {e}")
    
    async def detect_faces(self, frame: np.ndarray) -> List[FaceData]:
        """Detect faces in frame"""
        if not HAS_AI_LIBS:
            return []
        
        try:
            # Convert to grayscale for detection
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
            
            # Detect faces
            faces = self.face_cascade.detectMultiScale(
                gray, 
                scaleFactor=1.1, 
                minNeighbors=5, 
                minSize=(30, 30)
            )
            
            face_data = []
            for (x, y, w, h) in faces:
                # Normalize coordinates
                height, width = frame.shape[:2]
                face_data.append(FaceData(
                    x=x / width,
                    y=y / height,
                    width=w / width,
                    height=h / height,
                    confidence=1.0
                ))
            
            self.current_faces = face_data
            return face_data
            
        except Exception as e:
            logger.error(f"Error detecting faces: {e}")
            return []
    
    async def recognize_faces(self, frame: np.ndarray) -> List[FaceData]:
        """Recognize known faces in frame"""
        if not HAS_AI_LIBS or not self.known_faces:
            return await self.detect_faces(frame)
        
        try:
            # Find face locations and encodings
            face_locations = face_recognition.face_locations(frame)
            face_encodings = face_recognition.face_encodings(frame, face_locations)
            
            face_data = []
            for (top, right, bottom, left), face_encoding in zip(face_locations, face_encodings):
                # Check against known faces
                matches = face_recognition.compare_faces(
                    list(self.known_faces.values()), 
                    face_encoding,
                    tolerance=self.config.ai.face_recognition_tolerance
                )
                
                name = None
                if True in matches:
                    match_index = matches.index(True)
                    name = list(self.known_faces.keys())[match_index]
                
                # Normalize coordinates
                height, width = frame.shape[:2]
                face_data.append(FaceData(
                    x=left / width,
                    y=top / height,
                    width=(right - left) / width,
                    height=(bottom - top) / height,
                    confidence=1.0,
                    name=name
                ))
            
            self.current_faces = face_data
            return face_data
            
        except Exception as e:
            logger.error(f"Error recognizing faces: {e}")
            return []
    
    def get_primary_face_position(self) -> Optional[Dict[str, float]]:
        """Get position of primary (largest) detected face"""
        if not self.current_faces:
            return None
        
        # Find largest face
        largest_face = max(self.current_faces, key=lambda f: f.width * f.height)
        
        return {
            "x": largest_face.x + largest_face.width / 2,
            "y": largest_face.y + largest_face.height / 2,
            "confidence": largest_face.confidence,
            "name": largest_face.name
        }

class ComputerVisionEngine:
    """Computer vision processing"""
    
    def __init__(self, config: Config):
        self.config = config
        self.camera = None
        self.is_active = False
        self.current_frame = None
        self.frame_queue = queue.Queue(maxsize=5)
        
        # MediaPipe for gesture recognition
        if HAS_AI_LIBS:
            self.mp_hands = mp.solutions.hands
            self.hands = self.mp_hands.Hands(
                static_image_mode=False,
                max_num_hands=2,
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5
            )
    
    async def initialize(self):
        """Initialize computer vision"""
        try:
            # Initialize camera
            self.camera = cv2.VideoCapture(self.config.hardware.camera_index)
            self.camera.set(cv2.CAP_PROP_FRAME_WIDTH, self.config.ai.cv_frame_width)
            self.camera.set(cv2.CAP_PROP_FRAME_HEIGHT, self.config.ai.cv_frame_height)
            self.camera.set(cv2.CAP_PROP_FPS, self.config.ai.cv_fps)
            
            if not self.camera.isOpened():
                raise Exception("Failed to open camera")
            
            logger.info("Computer vision initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize computer vision: {e}")
            # Use dummy camera for testing
            self.camera = None
    
    async def start_capture(self):
        """Start video capture"""
        if self.is_active:
            return
        
        self.is_active = True
        
        def capture_frames():
            while self.is_active:
                if self.camera:
                    ret, frame = self.camera.read()
                    if ret:
                        # Add frame to queue (non-blocking)
                        try:
                            self.frame_queue.put_nowait(frame)
                            self.current_frame = frame
                        except queue.Full:
                            # Remove oldest frame and add new one
                            try:
                                self.frame_queue.get_nowait()
                                self.frame_queue.put_nowait(frame)
                            except queue.Empty:
                                pass
                else:
                    # Simulate frame for testing
                    dummy_frame = np.zeros((480, 640, 3), dtype=np.uint8)
                    self.current_frame = dummy_frame
                    asyncio.sleep(1.0 / 30)  # 30 FPS simulation
        
        # Start capture thread
        self.capture_thread = threading.Thread(target=capture_frames)
        self.capture_thread.daemon = True
        self.capture_thread.start()
        
        logger.info("Video capture started")
    
    async def stop_capture(self):
        """Stop video capture"""
        self.is_active = False
        if self.camera:
            self.camera.release()
        logger.info("Video capture stopped")
    
    def get_latest_frame(self) -> Optional[np.ndarray]:
        """Get latest captured frame"""
        return self.current_frame
    
    async def detect_gestures(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """Detect hand gestures in frame"""
        if not HAS_AI_LIBS:
            return []
        
        try:
            # Convert BGR to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            
            # Process frame
            results = self.hands.process(rgb_frame)
            
            gestures = []
            if results.multi_hand_landmarks:
                for hand_landmarks in results.multi_hand_landmarks:
                    # Extract landmark positions
                    landmarks = []
                    for landmark in hand_landmarks.landmark:
                        landmarks.append([landmark.x, landmark.y, landmark.z])
                    
                    # Simple gesture recognition (can be expanded)
                    gesture_type = self._classify_gesture(landmarks)
                    
                    gestures.append({
                        "type": gesture_type,
                        "landmarks": landmarks,
                        "confidence": 0.8
                    })
            
            return gestures
            
        except Exception as e:
            logger.error(f"Error detecting gestures: {e}")
            return []
    
    def _classify_gesture(self, landmarks: List[List[float]]) -> str:
        """Simple gesture classification"""
        # This is a simplified implementation
        # In practice, you'd use more sophisticated ML models
        
        if not landmarks:
            return "unknown"
        
        # Example: detect pointing gesture
        # This would need proper implementation based on hand landmarks
        return "pointing"

class AIEngine:
    """Main AI engine coordinating all AI services"""
    
    def __init__(self, config: Config):
        self.config = config
        self.voice_engine = VoiceRecognitionEngine(config)
        self.face_engine = FaceRecognitionEngine(config)
        self.cv_engine = ComputerVisionEngine(config)
        
        # Service states
        self.services_active = {
            "voice": False,
            "face": False,
            "cv": False
        }
    
    async def initialize(self):
        """Initialize all AI engines"""
        try:
            await self.voice_engine.initialize()
            await self.face_engine.initialize()
            await self.cv_engine.initialize()
            
            logger.info("AI engine initialized")
            
        except Exception as e:
            logger.error(f"Failed to initialize AI engine: {e}")
            raise
    
    async def start_voice_recognition(self):
        """Start voice recognition service"""
        await self.voice_engine.start_listening()
        self.services_active["voice"] = True
    
    async def stop_voice_recognition(self):
        """Stop voice recognition service"""
        await self.voice_engine.stop_listening()
        self.services_active["voice"] = False
    
    async def start_face_recognition(self):
        """Start face recognition service"""
        await self.cv_engine.start_capture()
        self.services_active["face"] = True
        self.services_active["cv"] = True
    
    async def stop_face_recognition(self):
        """Stop face recognition service"""
        self.services_active["face"] = False
        if not any(self.services_active.values()):
            await self.cv_engine.stop_capture()
            self.services_active["cv"] = False
    
    async def start_face_tracking(self):
        """Start face tracking"""
        await self.start_face_recognition()
    
    async def stop_face_tracking(self):
        """Stop face tracking"""
        await self.stop_face_recognition()
    
    async def speak_text(self, text: str, language: str = "en"):
        """Text-to-speech"""
        await self.voice_engine.speak_text(text, language)
    
    def get_voice_command(self) -> Optional[VoiceCommand]:
        """Get latest voice command"""
        return self.voice_engine.get_voice_command()
    
    async def get_face_position(self) -> Optional[Dict[str, float]]:
        """Get primary face position for tracking"""
        if not self.services_active["face"]:
            return None
        
        frame = self.cv_engine.get_latest_frame()
        if frame is not None:
            await self.face_engine.detect_faces(frame)
            return self.face_engine.get_primary_face_position()
        
        return None
    
    async def stop_all_services(self):
        """Stop all AI services"""
        await self.stop_voice_recognition()
        await self.stop_face_recognition()
        await self.cv_engine.stop_capture()
        
        for service in self.services_active:
            self.services_active[service] = False
    
    async def cleanup(self):
        """Cleanup AI engine resources"""
        await self.stop_all_services()
        logger.info("AI engine cleanup complete")
    
    def get_ai_status(self) -> Dict[str, Any]:
        """Get AI engine status"""
        return {
            "services_active": self.services_active,
            "has_ai_libs": HAS_AI_LIBS,
            "voice_available": HAS_AI_LIBS,
            "face_recognition_available": HAS_AI_LIBS,
            "computer_vision_available": HAS_AI_LIBS
        }
