"""
Hardware Manager - Controls all physical robot components
Servo motors, DC motors, sensors, and I/O devices
"""

import asyncio
import logging
import time
import math
from typing import Dict, Any, Optional, Tuple
from dataclasses import dataclass

try:
    import RPi.GPIO as GPIO
    from adafruit_servokit import ServoKit
    import board
    import busio
    import adafruit_mpu6050
    HAS_HARDWARE = True
except ImportError:
    HAS_HARDWARE = False
    logging.warning("Hardware libraries not available - running in simulation mode")

from core.config import Config, ServoConfig, MotorConfig

logger = logging.getLogger(__name__)

@dataclass
class SensorData:
    """Sensor data container"""
    accelerometer: Tuple[float, float, float] = (0.0, 0.0, 0.0)
    gyroscope: Tuple[float, float, float] = (0.0, 0.0, 0.0)
    temperature: float = 25.0
    ultrasonic_distance: float = 0.0
    battery_voltage: float = 7.4

class ServoController:
    """Individual servo motor controller"""
    
    def __init__(self, config: ServoConfig, servo_kit, channel: int):
        self.config = config
        self.servo_kit = servo_kit
        self.channel = channel
        self.current_angle = config.default_angle
        self.target_angle = config.default_angle
        self.is_moving = False
        
        # Set initial position
        if HAS_HARDWARE:
            self.servo_kit.servo[channel].angle = self.current_angle
    
    async def set_angle(self, angle: float, speed: float = 1.0):
        """Set servo angle with optional speed control"""
        # Clamp angle to valid range
        angle = max(self.config.min_angle, min(self.config.max_angle, angle))
        
        if not HAS_HARDWARE:
            # Simulation mode
            self.current_angle = angle
            self.target_angle = angle
            return
        
        self.target_angle = angle
        
        if speed <= 0:
            # Immediate movement
            self.servo_kit.servo[self.channel].angle = angle
            self.current_angle = angle
        else:
            # Smooth movement
            await self._smooth_move_to_angle(angle, speed)
    
    async def _smooth_move_to_angle(self, target_angle: float, speed: float):
        """Smoothly move servo to target angle"""
        self.is_moving = True
        
        start_angle = self.current_angle
        angle_diff = target_angle - start_angle
        
        # Calculate movement time based on speed
        move_time = abs(angle_diff) / (speed * 90)  # speed in degrees per second
        steps = max(10, int(move_time * 50))  # 50 Hz update rate
        
        for i in range(steps + 1):
            progress = i / steps
            current_angle = start_angle + (angle_diff * progress)
            
            self.servo_kit.servo[self.channel].angle = current_angle
            self.current_angle = current_angle
            
            await asyncio.sleep(move_time / steps)
        
        self.is_moving = False
    
    def get_angle(self) -> float:
        """Get current servo angle"""
        return self.current_angle

class MotorController:
    """DC motor controller with encoder support"""
    
    def __init__(self, config: MotorConfig):
        self.config = config
        self.current_speed = 0.0
        self.target_speed = 0.0
        self.encoder_count = 0
        
        if HAS_HARDWARE:
            # Setup GPIO pins
            GPIO.setup(config.pin_a, GPIO.OUT)
            GPIO.setup(config.pin_b, GPIO.OUT)
            GPIO.setup(config.enable_pin, GPIO.OUT)
            
            # Setup PWM for speed control
            self.pwm = GPIO.PWM(config.enable_pin, 1000)  # 1kHz frequency
            self.pwm.start(0)
            
            # Setup encoder if available
            if config.encoder_pin_a:
                GPIO.setup(config.encoder_pin_a, GPIO.IN, pull_up_down=GPIO.PUD_UP)
                GPIO.add_event_detect(config.encoder_pin_a, GPIO.RISING, 
                                    callback=self._encoder_callback)
    
    def _encoder_callback(self, channel):
        """Encoder interrupt callback"""
        self.encoder_count += 1
    
    async def set_speed(self, speed: float):
        """Set motor speed (-1.0 to 1.0)"""
        speed = max(-1.0, min(1.0, speed))
        self.target_speed = speed
        
        if not HAS_HARDWARE:
            self.current_speed = speed
            return
        
        # Set direction
        if speed > 0:
            GPIO.output(self.config.pin_a, GPIO.HIGH)
            GPIO.output(self.config.pin_b, GPIO.LOW)
        elif speed < 0:
            GPIO.output(self.config.pin_a, GPIO.LOW)
            GPIO.output(self.config.pin_b, GPIO.HIGH)
        else:
            GPIO.output(self.config.pin_a, GPIO.LOW)
            GPIO.output(self.config.pin_b, GPIO.LOW)
        
        # Set PWM duty cycle
        duty_cycle = abs(speed) * 100
        self.pwm.ChangeDutyCycle(duty_cycle)
        self.current_speed = speed
    
    async def stop(self):
        """Stop motor"""
        await self.set_speed(0.0)
    
    def get_encoder_count(self) -> int:
        """Get encoder count"""
        return self.encoder_count
    
    def reset_encoder(self):
        """Reset encoder count"""
        self.encoder_count = 0

class HardwareManager:
    """Main hardware management class"""
    
    def __init__(self, config: Config):
        self.config = config
        self.servos: Dict[str, ServoController] = {}
        self.motors: Dict[str, MotorController] = {}
        self.sensor_data = SensorData()
        
        # Hardware components
        self.servo_kit = None
        self.mpu6050 = None
        self.i2c = None
        
        # Monitoring flags
        self.sensor_monitoring_active = False
        
        logger.info("Hardware manager initialized")
    
    async def initialize(self):
        """Initialize all hardware components"""
        try:
            if HAS_HARDWARE:
                # Initialize GPIO
                GPIO.setmode(GPIO.BCM)
                GPIO.setwarnings(False)
                
                # Initialize servo kit
                self.servo_kit = ServoKit(channels=16)
                
                # Initialize I2C and sensors
                self.i2c = busio.I2C(board.SCL, board.SDA)
                self.mpu6050 = adafruit_mpu6050.MPU6050(self.i2c)
                
                logger.info("Hardware components initialized")
            
            # Initialize servos
            await self._initialize_servos()
            
            # Initialize motors
            await self._initialize_motors()
            
            # Setup additional GPIO pins
            await self._setup_gpio_pins()
            
            logger.info("Hardware manager initialization complete")
            
        except Exception as e:
            logger.error(f"Failed to initialize hardware: {e}")
            raise
    
    async def _initialize_servos(self):
        """Initialize servo controllers"""
        for name, servo_config in self.config.hardware.servos.items():
            controller = ServoController(
                servo_config, 
                self.servo_kit, 
                servo_config.pin
            )
            self.servos[name] = controller
            logger.info(f"Initialized servo: {name} on pin {servo_config.pin}")
    
    async def _initialize_motors(self):
        """Initialize motor controllers"""
        for name, motor_config in self.config.hardware.motors.items():
            controller = MotorController(motor_config)
            self.motors[name] = controller
            logger.info(f"Initialized motor: {name}")
    
    async def _setup_gpio_pins(self):
        """Setup additional GPIO pins"""
        if not HAS_HARDWARE:
            return
        
        # Setup ultrasonic sensor
        GPIO.setup(self.config.hardware.ultrasonic_trigger, GPIO.OUT)
        GPIO.setup(self.config.hardware.ultrasonic_echo, GPIO.IN)
        
        # Setup LED and button
        GPIO.setup(self.config.hardware.led_pin, GPIO.OUT)
        GPIO.setup(self.config.hardware.button_pin, GPIO.IN, pull_up_down=GPIO.PUD_UP)
    
    async def set_servo_angle(self, servo_name: str, angle: float, speed: float = 1.0):
        """Set servo angle by name"""
        if servo_name in self.servos:
            await self.servos[servo_name].set_angle(angle, speed)
            logger.debug(f"Set {servo_name} to {angle} degrees")
        else:
            logger.warning(f"Servo {servo_name} not found")
    
    async def get_servo_angle(self, servo_name: str) -> Optional[float]:
        """Get current servo angle"""
        if servo_name in self.servos:
            return self.servos[servo_name].get_angle()
        return None
    
    async def move_robot(self, linear: float, angular: float, duration: float = 0.0):
        """Move robot with differential drive"""
        # Calculate wheel speeds for differential drive
        left_speed, right_speed = self._calculate_wheel_speeds(linear, angular)
        
        # Set motor speeds
        if "left_wheel" in self.motors:
            await self.motors["left_wheel"].set_speed(left_speed)
        if "right_wheel" in self.motors:
            await self.motors["right_wheel"].set_speed(right_speed)
        
        # If duration specified, stop after duration
        if duration > 0:
            await asyncio.sleep(duration)
            await self.stop_robot()
        
        logger.debug(f"Robot moving: linear={linear}, angular={angular}")
    
    def _calculate_wheel_speeds(self, linear: float, angular: float) -> Tuple[float, float]:
        """Calculate individual wheel speeds for differential drive"""
        # Differential drive kinematics
        # Assuming wheel separation of 0.2m (adjust as needed)
        wheel_separation = 0.2
        
        left_speed = linear - (angular * wheel_separation / 2)
        right_speed = linear + (angular * wheel_separation / 2)
        
        # Normalize speeds to [-1, 1] range
        max_speed = max(abs(left_speed), abs(right_speed))
        if max_speed > 1.0:
            left_speed /= max_speed
            right_speed /= max_speed
        
        return left_speed, right_speed
    
    async def stop_robot(self):
        """Stop all robot movement"""
        for motor in self.motors.values():
            await motor.stop()
        logger.debug("Robot stopped")
    
    async def stop_all_motors(self):
        """Emergency stop all motors"""
        await self.stop_robot()
    
    async def start_sensor_monitoring(self):
        """Start continuous sensor monitoring"""
        self.sensor_monitoring_active = True
        asyncio.create_task(self._sensor_monitoring_loop())
        logger.info("Sensor monitoring started")
    
    async def stop_sensor_monitoring(self):
        """Stop sensor monitoring"""
        self.sensor_monitoring_active = False
        logger.info("Sensor monitoring stopped")
    
    async def _sensor_monitoring_loop(self):
        """Continuous sensor monitoring loop"""
        while self.sensor_monitoring_active:
            try:
                # Read IMU data
                if HAS_HARDWARE and self.mpu6050:
                    accel = self.mpu6050.acceleration
                    gyro = self.mpu6050.gyro
                    temp = self.mpu6050.temperature
                    
                    self.sensor_data.accelerometer = accel
                    self.sensor_data.gyroscope = gyro
                    self.sensor_data.temperature = temp
                
                # Read ultrasonic distance
                distance = await self._read_ultrasonic_distance()
                self.sensor_data.ultrasonic_distance = distance
                
                # Read battery voltage (simulated)
                self.sensor_data.battery_voltage = 7.4  # Would read from ADC
                
                await asyncio.sleep(0.1)  # 10Hz update rate
                
            except Exception as e:
                logger.error(f"Error in sensor monitoring: {e}")
                await asyncio.sleep(1.0)
    
    async def _read_ultrasonic_distance(self) -> float:
        """Read distance from ultrasonic sensor"""
        if not HAS_HARDWARE:
            return 1.0  # Simulated distance
        
        try:
            # Trigger pulse
            GPIO.output(self.config.hardware.ultrasonic_trigger, True)
            await asyncio.sleep(0.00001)  # 10µs pulse
            GPIO.output(self.config.hardware.ultrasonic_trigger, False)
            
            # Measure echo time
            start_time = time.time()
            while GPIO.input(self.config.hardware.ultrasonic_echo) == 0:
                start_time = time.time()
                if time.time() - start_time > 0.1:  # Timeout
                    return 0.0
            
            end_time = time.time()
            while GPIO.input(self.config.hardware.ultrasonic_echo) == 1:
                end_time = time.time()
                if end_time - start_time > 0.1:  # Timeout
                    return 0.0
            
            # Calculate distance
            pulse_duration = end_time - start_time
            distance = (pulse_duration * 34300) / 2  # Speed of sound = 343 m/s
            
            return distance / 100  # Convert to meters
            
        except Exception as e:
            logger.error(f"Error reading ultrasonic sensor: {e}")
            return 0.0
    
    def get_sensor_data(self) -> SensorData:
        """Get current sensor data"""
        return self.sensor_data
    
    async def set_led(self, state: bool):
        """Control LED"""
        if HAS_HARDWARE:
            GPIO.output(self.config.hardware.led_pin, state)
    
    async def read_button(self) -> bool:
        """Read button state"""
        if HAS_HARDWARE:
            return not GPIO.input(self.config.hardware.button_pin)  # Inverted for pull-up
        return False
    
    async def cleanup(self):
        """Cleanup hardware resources"""
        try:
            # Stop all motors
            await self.stop_all_motors()
            
            # Stop sensor monitoring
            await self.stop_sensor_monitoring()
            
            # Cleanup GPIO
            if HAS_HARDWARE:
                for motor in self.motors.values():
                    if hasattr(motor, 'pwm'):
                        motor.pwm.stop()
                GPIO.cleanup()
            
            logger.info("Hardware cleanup complete")
            
        except Exception as e:
            logger.error(f"Error during hardware cleanup: {e}")
    
    def get_hardware_status(self) -> Dict[str, Any]:
        """Get hardware status information"""
        return {
            "servos": {name: servo.get_angle() for name, servo in self.servos.items()},
            "motors": {name: motor.current_speed for name, motor in self.motors.items()},
            "sensors": {
                "accelerometer": self.sensor_data.accelerometer,
                "gyroscope": self.sensor_data.gyroscope,
                "temperature": self.sensor_data.temperature,
                "ultrasonic_distance": self.sensor_data.ultrasonic_distance,
                "battery_voltage": self.sensor_data.battery_voltage
            },
            "hardware_available": HAS_HARDWARE
        }
