import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Paper
} from '@mui/material';
import {
  <PERSON>Up<PERSON>,
  ArrowDownward,
  ArrowB<PERSON>,
  ArrowFor<PERSON>,
  Stop,
  RotateLeft,
  RotateRight
} from '@mui/icons-material';

function RobotControl({ sendRobotCommand, addNotification }) {
  const [speed, setSpeed] = useState(0.3);
  const [selectedServo, setSelectedServo] = useState('neck');
  const [servoAngle, setServoAngle] = useState(90);
  
  const servos = [
    { value: 'neck', label: 'Neck' },
    { value: 'left_shoulder', label: 'Left Shoulder' },
    { value: 'left_elbow', label: 'Left Elbow' },
    { value: 'right_shoulder', label: 'Right Shoulder' },
    { value: 'right_elbow', label: 'Right Elbow' }
  ];
  
  const handleMove = async (direction) => {
    const movements = {
      forward: { linear: speed, angular: 0 },
      backward: { linear: -speed, angular: 0 },
      left: { linear: 0, angular: speed },
      right: { linear: 0, angular: -speed },
      stop: { linear: 0, angular: 0 }
    };
    
    const movement = movements[direction];
    await sendRobotCommand({
      type: 'move',
      params: { ...movement, duration: direction === 'stop' ? 0 : 2 }
    });
    
    addNotification(`Robot moving ${direction}`, 'info');
  };
  
  const handleServoMove = async () => {
    await sendRobotCommand({
      type: 'servo',
      params: { servo: selectedServo, angle: servoAngle }
    });
    
    addNotification(`${selectedServo} moved to ${servoAngle}°`, 'info');
  };
  
  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 600, color: 'white', mb: 3 }}>
        Robot Control
      </Typography>
      
      <Grid container spacing={3}>
        {/* Movement Control */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Movement Control
              </Typography>
              
              <Box sx={{ mb: 3 }}>
                <Typography gutterBottom>Speed: {speed.toFixed(1)}</Typography>
                <Slider
                  value={speed}
                  onChange={(e, value) => setSpeed(value)}
                  min={0.1}
                  max={1.0}
                  step={0.1}
                  marks
                  valueLabelDisplay="auto"
                />
              </Box>
              
              <Grid container spacing={1} sx={{ textAlign: 'center' }}>
                <Grid item xs={12}>
                  <IconButton
                    size="large"
                    onClick={() => handleMove('forward')}
                    sx={{ bgcolor: 'primary.main', color: 'white', m: 0.5 }}
                  >
                    <ArrowUpward />
                  </IconButton>
                </Grid>
                
                <Grid item xs={4}>
                  <IconButton
                    size="large"
                    onClick={() => handleMove('left')}
                    sx={{ bgcolor: 'primary.main', color: 'white', m: 0.5 }}
                  >
                    <ArrowBack />
                  </IconButton>
                </Grid>
                
                <Grid item xs={4}>
                  <IconButton
                    size="large"
                    onClick={() => handleMove('stop')}
                    sx={{ bgcolor: 'error.main', color: 'white', m: 0.5 }}
                  >
                    <Stop />
                  </IconButton>
                </Grid>
                
                <Grid item xs={4}>
                  <IconButton
                    size="large"
                    onClick={() => handleMove('right')}
                    sx={{ bgcolor: 'primary.main', color: 'white', m: 0.5 }}
                  >
                    <ArrowForward />
                  </IconButton>
                </Grid>
                
                <Grid item xs={12}>
                  <IconButton
                    size="large"
                    onClick={() => handleMove('backward')}
                    sx={{ bgcolor: 'primary.main', color: 'white', m: 0.5 }}
                  >
                    <ArrowDownward />
                  </IconButton>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Servo Control */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Servo Control
              </Typography>
              
              <FormControl fullWidth sx={{ mb: 2 }}>
                <InputLabel>Servo</InputLabel>
                <Select
                  value={selectedServo}
                  onChange={(e) => setSelectedServo(e.target.value)}
                  label="Servo"
                >
                  {servos.map((servo) => (
                    <MenuItem key={servo.value} value={servo.value}>
                      {servo.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
              
              <Box sx={{ mb: 3 }}>
                <Typography gutterBottom>Angle: {servoAngle}°</Typography>
                <Slider
                  value={servoAngle}
                  onChange={(e, value) => setServoAngle(value)}
                  min={0}
                  max={180}
                  step={5}
                  marks
                  valueLabelDisplay="auto"
                />
              </Box>
              
              <Button
                fullWidth
                variant="contained"
                onClick={handleServoMove}
              >
                Move Servo
              </Button>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default RobotControl;
