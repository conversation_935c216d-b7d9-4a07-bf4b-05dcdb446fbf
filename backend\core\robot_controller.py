"""
Main Robot Controller - Central coordination of all robot systems
Inspired by UBTech Alpha 1E architecture with enhanced capabilities
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, List
from enum import Enum
from dataclasses import dataclass

from .config import Config
from hardware.hardware_manager import HardwareManager
from ai.ai_engine import AIEngine
from api.websocket_manager import WebSocketManager

logger = logging.getLogger(__name__)

class RobotState(Enum):
    """Robot operational states"""
    INITIALIZING = "initializing"
    IDLE = "idle"
    LISTENING = "listening"
    PROCESSING = "processing"
    MOVING = "moving"
    INTERACTING = "interacting"
    DANCING = "dancing"
    PROGRAMMING = "programming"
    ERROR = "error"
    SLEEPING = "sleeping"

@dataclass
class RobotStatus:
    """Robot status information"""
    state: RobotState
    battery_level: float
    cpu_usage: float
    memory_usage: float
    temperature: float
    uptime: float
    last_command: Optional[str] = None
    error_message: Optional[str] = None

class RobotController:
    """Main robot controller coordinating all subsystems"""
    
    def __init__(self, config: Config, hardware_manager: HardwareManager, 
                 ai_engine: <PERSON><PERSON><PERSON><PERSON>, websocket_manager: WebSocketManager):
        self.config = config
        self.hardware = hardware_manager
        self.ai = ai_engine
        self.websocket = websocket_manager
        
        # Robot state
        self.state = RobotState.INITIALIZING
        self.start_time = time.time()
        self.last_activity = time.time()
        
        # Command queue
        self.command_queue = asyncio.Queue()
        self.is_running = False
        
        # Behavior flags
        self.is_autonomous = False
        self.voice_enabled = True
        self.face_tracking_enabled = True
        
        # Current activities
        self.current_program = None
        self.current_dance = None
        
        logger.info("Robot controller initialized")
    
    async def initialize(self):
        """Initialize robot controller"""
        try:
            # Set initial servo positions
            await self._set_initial_positions()
            
            # Start AI services
            await self.ai.start_face_recognition()
            await self.ai.start_voice_recognition()
            
            # Initialize sensors
            await self.hardware.start_sensor_monitoring()
            
            # Set state to idle
            self.state = RobotState.IDLE
            self.is_running = True
            
            logger.info("Robot controller initialization complete")
            
        except Exception as e:
            self.state = RobotState.ERROR
            logger.error(f"Failed to initialize robot controller: {e}")
            raise
    
    async def start_main_loop(self):
        """Main robot control loop"""
        logger.info("Starting main robot control loop")
        
        while self.is_running:
            try:
                # Process commands from queue
                await self._process_command_queue()
                
                # Update robot status
                await self._update_status()
                
                # Handle autonomous behaviors
                if self.is_autonomous:
                    await self._handle_autonomous_behavior()
                
                # Check for idle timeout
                await self._check_idle_timeout()
                
                # Broadcast status to connected clients
                await self._broadcast_status()
                
                # Small delay to prevent excessive CPU usage
                await asyncio.sleep(0.1)
                
            except Exception as e:
                logger.error(f"Error in main loop: {e}")
                await asyncio.sleep(1.0)
    
    async def _set_initial_positions(self):
        """Set servos to initial positions"""
        initial_positions = {
            "neck": 90,
            "left_shoulder": 90,
            "left_elbow": 90,
            "right_shoulder": 90,
            "right_elbow": 90
        }
        
        for servo_name, angle in initial_positions.items():
            await self.hardware.set_servo_angle(servo_name, angle)
        
        logger.info("Set initial servo positions")
    
    async def _process_command_queue(self):
        """Process commands from the command queue"""
        try:
            # Non-blocking queue check
            command = await asyncio.wait_for(
                self.command_queue.get(), timeout=0.01
            )
            
            await self._execute_command(command)
            self.command_queue.task_done()
            
        except asyncio.TimeoutError:
            # No commands in queue
            pass
    
    async def _execute_command(self, command: Dict[str, Any]):
        """Execute a robot command"""
        command_type = command.get("type")
        params = command.get("params", {})
        
        logger.info(f"Executing command: {command_type}")
        
        try:
            if command_type == "move":
                await self._handle_move_command(params)
            elif command_type == "servo":
                await self._handle_servo_command(params)
            elif command_type == "speak":
                await self._handle_speak_command(params)
            elif command_type == "dance":
                await self._handle_dance_command(params)
            elif command_type == "program":
                await self._handle_program_command(params)
            elif command_type == "face_track":
                await self._handle_face_track_command(params)
            elif command_type == "autonomous":
                await self._handle_autonomous_command(params)
            else:
                logger.warning(f"Unknown command type: {command_type}")
        
        except Exception as e:
            logger.error(f"Error executing command {command_type}: {e}")
            self.state = RobotState.ERROR
    
    async def _handle_move_command(self, params: Dict[str, Any]):
        """Handle movement commands"""
        self.state = RobotState.MOVING
        
        linear = params.get("linear", 0.0)
        angular = params.get("angular", 0.0)
        duration = params.get("duration", 1.0)
        
        await self.hardware.move_robot(linear, angular, duration)
        
        self.state = RobotState.IDLE
        self.last_activity = time.time()
    
    async def _handle_servo_command(self, params: Dict[str, Any]):
        """Handle servo movement commands"""
        servo_name = params.get("servo")
        angle = params.get("angle")
        speed = params.get("speed", 1.0)
        
        if servo_name and angle is not None:
            await self.hardware.set_servo_angle(servo_name, angle, speed)
            self.last_activity = time.time()
    
    async def _handle_speak_command(self, params: Dict[str, Any]):
        """Handle text-to-speech commands"""
        self.state = RobotState.INTERACTING
        
        text = params.get("text", "")
        language = params.get("language", "en")
        
        await self.ai.speak_text(text, language)
        
        self.state = RobotState.IDLE
        self.last_activity = time.time()
    
    async def _handle_dance_command(self, params: Dict[str, Any]):
        """Handle dance sequence commands"""
        self.state = RobotState.DANCING
        
        dance_name = params.get("dance", "default")
        await self._execute_dance_sequence(dance_name)
        
        self.state = RobotState.IDLE
        self.last_activity = time.time()
    
    async def _handle_program_command(self, params: Dict[str, Any]):
        """Handle Blockly program execution"""
        self.state = RobotState.PROGRAMMING
        
        program_code = params.get("code", "")
        await self._execute_blockly_program(program_code)
        
        self.state = RobotState.IDLE
        self.last_activity = time.time()
    
    async def _handle_face_track_command(self, params: Dict[str, Any]):
        """Handle face tracking commands"""
        enabled = params.get("enabled", True)
        self.face_tracking_enabled = enabled
        
        if enabled:
            await self.ai.start_face_tracking()
        else:
            await self.ai.stop_face_tracking()
    
    async def _handle_autonomous_command(self, params: Dict[str, Any]):
        """Handle autonomous mode commands"""
        self.is_autonomous = params.get("enabled", False)
        logger.info(f"Autonomous mode: {self.is_autonomous}")
    
    async def _execute_dance_sequence(self, dance_name: str):
        """Execute a predefined dance sequence"""
        # Define dance sequences
        dance_sequences = {
            "wave": [
                {"servo": "right_shoulder", "angle": 45, "duration": 0.5},
                {"servo": "right_elbow", "angle": 45, "duration": 0.5},
                {"servo": "right_shoulder", "angle": 135, "duration": 0.5},
                {"servo": "right_elbow", "angle": 135, "duration": 0.5},
                {"servo": "right_shoulder", "angle": 90, "duration": 0.5},
                {"servo": "right_elbow", "angle": 90, "duration": 0.5}
            ],
            "nod": [
                {"servo": "neck", "angle": 60, "duration": 0.5},
                {"servo": "neck", "angle": 120, "duration": 0.5},
                {"servo": "neck", "angle": 90, "duration": 0.5}
            ],
            "celebrate": [
                {"servo": "left_shoulder", "angle": 45, "duration": 0.3},
                {"servo": "right_shoulder", "angle": 45, "duration": 0.3},
                {"servo": "left_elbow", "angle": 45, "duration": 0.3},
                {"servo": "right_elbow", "angle": 45, "duration": 0.3},
                {"servo": "left_shoulder", "angle": 135, "duration": 0.3},
                {"servo": "right_shoulder", "angle": 135, "duration": 0.3},
                {"servo": "left_shoulder", "angle": 90, "duration": 0.5},
                {"servo": "right_shoulder", "angle": 90, "duration": 0.5},
                {"servo": "left_elbow", "angle": 90, "duration": 0.5},
                {"servo": "right_elbow", "angle": 90, "duration": 0.5}
            ]
        }
        
        sequence = dance_sequences.get(dance_name, dance_sequences["wave"])
        
        for move in sequence:
            await self.hardware.set_servo_angle(
                move["servo"], 
                move["angle"], 
                1.0 / move["duration"]
            )
            await asyncio.sleep(move["duration"])
    
    async def _execute_blockly_program(self, program_code: str):
        """Execute Blockly-generated program"""
        # This would integrate with a Blockly interpreter
        # For now, we'll implement a simple command parser
        
        try:
            # Parse and execute Blockly commands
            # This is a simplified implementation
            commands = self._parse_blockly_code(program_code)
            
            for command in commands:
                await self._execute_command(command)
                
        except Exception as e:
            logger.error(f"Error executing Blockly program: {e}")
    
    def _parse_blockly_code(self, code: str) -> List[Dict[str, Any]]:
        """Parse Blockly-generated code into commands"""
        # Simplified parser - in reality, this would be more sophisticated
        commands = []
        
        # Example parsing logic
        lines = code.strip().split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('move('):
                # Parse move command
                pass
            elif line.startswith('servo('):
                # Parse servo command
                pass
            elif line.startswith('speak('):
                # Parse speak command
                pass
        
        return commands
    
    async def _handle_autonomous_behavior(self):
        """Handle autonomous robot behaviors"""
        if not self.is_autonomous:
            return
        
        # Simple autonomous behaviors
        current_time = time.time()
        
        # Face tracking
        if self.face_tracking_enabled:
            face_position = await self.ai.get_face_position()
            if face_position:
                await self._track_face(face_position)
        
        # Random movements every 30 seconds
        if current_time - self.last_activity > 30:
            await self._perform_random_action()
            self.last_activity = current_time
    
    async def _track_face(self, face_position: Dict[str, float]):
        """Track detected face with neck servo"""
        x, y = face_position.get("x", 0.5), face_position.get("y", 0.5)
        
        # Convert face position to neck angle
        neck_angle = 90 + (x - 0.5) * 60  # ±30 degrees from center
        neck_angle = max(30, min(150, neck_angle))  # Limit range
        
        await self.hardware.set_servo_angle("neck", neck_angle, 2.0)
    
    async def _perform_random_action(self):
        """Perform a random autonomous action"""
        import random
        
        actions = ["wave", "nod", "look_around"]
        action = random.choice(actions)
        
        if action == "wave":
            await self._execute_dance_sequence("wave")
        elif action == "nod":
            await self._execute_dance_sequence("nod")
        elif action == "look_around":
            # Look left and right
            await self.hardware.set_servo_angle("neck", 45, 1.0)
            await asyncio.sleep(1.0)
            await self.hardware.set_servo_angle("neck", 135, 1.0)
            await asyncio.sleep(1.0)
            await self.hardware.set_servo_angle("neck", 90, 1.0)
    
    async def _check_idle_timeout(self):
        """Check for idle timeout and enter sleep mode"""
        if not self.config.robot.auto_sleep:
            return
        
        idle_time = time.time() - self.last_activity
        if idle_time > self.config.robot.idle_timeout:
            if self.state != RobotState.SLEEPING:
                await self._enter_sleep_mode()
    
    async def _enter_sleep_mode(self):
        """Enter sleep mode to conserve power"""
        self.state = RobotState.SLEEPING
        
        # Move to sleep position
        sleep_positions = {
            "neck": 90,
            "left_shoulder": 90,
            "left_elbow": 90,
            "right_shoulder": 90,
            "right_elbow": 90
        }
        
        for servo_name, angle in sleep_positions.items():
            await self.hardware.set_servo_angle(servo_name, angle, 0.5)
        
        logger.info("Robot entered sleep mode")
    
    async def wake_up(self):
        """Wake up from sleep mode"""
        if self.state == RobotState.SLEEPING:
            self.state = RobotState.IDLE
            self.last_activity = time.time()
            
            # Perform wake-up sequence
            await self._execute_dance_sequence("nod")
            await self.ai.speak_text("Hello! I'm awake now.")
            
            logger.info("Robot woke up")
    
    async def _update_status(self):
        """Update robot status information"""
        # This would collect real system information
        pass
    
    async def _broadcast_status(self):
        """Broadcast status to connected clients"""
        status = self.get_status()
        await self.websocket.broadcast({
            "type": "status_update",
            "data": status
        })
    
    def get_status(self) -> Dict[str, Any]:
        """Get current robot status"""
        return {
            "state": self.state.value,
            "uptime": time.time() - self.start_time,
            "last_activity": self.last_activity,
            "is_autonomous": self.is_autonomous,
            "voice_enabled": self.voice_enabled,
            "face_tracking_enabled": self.face_tracking_enabled
        }
    
    async def add_command(self, command: Dict[str, Any]):
        """Add command to execution queue"""
        await self.command_queue.put(command)
    
    async def shutdown(self):
        """Shutdown robot controller"""
        self.is_running = False
        
        # Stop all activities
        await self.ai.stop_all_services()
        await self.hardware.stop_all_motors()
        
        logger.info("Robot controller shutdown complete")
