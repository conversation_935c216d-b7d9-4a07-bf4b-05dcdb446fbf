#!/bin/bash

# STEM_XPERT Robot Startup Script
# This script starts all robot services in the correct order

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if running as root (required for GPIO access)
if [[ $EUID -eq 0 ]]; then
   warning "Running as root. This is required for GPIO access but not recommended for security."
fi

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

log "Starting STEM_XPERT Robot..."
log "Project directory: $PROJECT_DIR"

# Check if virtual environment exists
if [ ! -d "$PROJECT_DIR/venv" ]; then
    warning "Virtual environment not found. Creating one..."
    cd "$PROJECT_DIR"
    python3 -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
else
    log "Activating virtual environment..."
    source "$PROJECT_DIR/venv/bin/activate"
fi

# Check if required packages are installed
log "Checking Python dependencies..."
python -c "import fastapi, uvicorn, opencv-python" 2>/dev/null || {
    error "Required Python packages not found. Installing..."
    pip install -r "$PROJECT_DIR/requirements.txt"
}

# Check hardware availability
log "Checking hardware availability..."
if command -v gpio &> /dev/null; then
    success "GPIO tools available"
else
    warning "GPIO tools not found. Hardware control may not work."
fi

# Check camera
if [ -e /dev/video0 ]; then
    success "Camera detected at /dev/video0"
else
    warning "No camera detected. Computer vision features may not work."
fi

# Check audio devices
if command -v aplay &> /dev/null && aplay -l | grep -q "card"; then
    success "Audio devices detected"
else
    warning "No audio devices detected. Voice features may not work."
fi

# Create necessary directories
log "Creating necessary directories..."
mkdir -p "$PROJECT_DIR/logs"
mkdir -p "$PROJECT_DIR/models"
mkdir -p "$PROJECT_DIR/data"

# Set permissions for GPIO (if running on Raspberry Pi)
if [ -e /dev/gpiomem ]; then
    log "Setting GPIO permissions..."
    sudo chmod 666 /dev/gpiomem 2>/dev/null || warning "Could not set GPIO permissions"
fi

# Start the backend server
log "Starting backend server..."
cd "$PROJECT_DIR/backend"

# Set environment variables
export PYTHONPATH="$PROJECT_DIR/backend:$PYTHONPATH"
export ROBOT_CONFIG_PATH="$PROJECT_DIR/config/robot_config.yaml"
export ROBOT_LOG_LEVEL="INFO"

# Start the server with proper logging
python main.py > "$PROJECT_DIR/logs/robot.log" 2>&1 &
BACKEND_PID=$!

# Wait a moment for the server to start
sleep 3

# Check if backend is running
if kill -0 $BACKEND_PID 2>/dev/null; then
    success "Backend server started (PID: $BACKEND_PID)"
    echo $BACKEND_PID > "$PROJECT_DIR/logs/backend.pid"
else
    error "Failed to start backend server"
    exit 1
fi

# Check if frontend build exists
if [ -d "$PROJECT_DIR/frontend/build" ]; then
    success "Frontend build found - will be served by backend"
else
    warning "Frontend build not found. Run 'npm run build' in frontend directory."
    
    # Optionally start development server
    if [ -f "$PROJECT_DIR/frontend/package.json" ]; then
        log "Starting frontend development server..."
        cd "$PROJECT_DIR/frontend"
        npm start > "$PROJECT_DIR/logs/frontend.log" 2>&1 &
        FRONTEND_PID=$!
        echo $FRONTEND_PID > "$PROJECT_DIR/logs/frontend.pid"
        success "Frontend development server started (PID: $FRONTEND_PID)"
    fi
fi

# Wait for services to be ready
log "Waiting for services to be ready..."
sleep 5

# Test API endpoint
if curl -s http://localhost:8000/health > /dev/null; then
    success "Robot API is responding"
else
    error "Robot API is not responding"
    exit 1
fi

# Display status
log "STEM_XPERT Robot is now running!"
echo ""
echo "🤖 Robot Status:"
echo "   Backend API: http://localhost:8000"
echo "   WebSocket: ws://localhost:8000/ws"
echo "   Health Check: http://localhost:8000/health"
echo ""
echo "🌐 Web Interface:"
if [ -d "$PROJECT_DIR/frontend/build" ]; then
    echo "   Production: http://localhost:8000"
else
    echo "   Development: http://localhost:3000"
fi
echo ""
echo "📋 Logs:"
echo "   Backend: $PROJECT_DIR/logs/robot.log"
echo "   Frontend: $PROJECT_DIR/logs/frontend.log"
echo ""
echo "🛑 To stop the robot:"
echo "   Run: $SCRIPT_DIR/stop_robot.sh"
echo ""

# Keep script running to monitor services
log "Monitoring services... Press Ctrl+C to stop"

# Trap Ctrl+C to clean shutdown
trap 'log "Shutting down..."; $SCRIPT_DIR/stop_robot.sh; exit 0' INT

# Monitor loop
while true; do
    sleep 30
    
    # Check if backend is still running
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        error "Backend server has stopped unexpectedly"
        exit 1
    fi
    
    # Check API health
    if ! curl -s http://localhost:8000/health > /dev/null; then
        warning "API health check failed"
    fi
done
