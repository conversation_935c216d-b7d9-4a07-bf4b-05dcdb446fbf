"""
Configuration management for STEM_XPERT Robot
Centralized configuration with environment variable support
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field
from dataclasses import dataclass

@dataclass
class ServoConfig:
    """Servo motor configuration"""
    pin: int
    min_pulse: int = 500
    max_pulse: int = 2500
    min_angle: int = 0
    max_angle: int = 180
    default_angle: int = 90
    speed: float = 1.0

@dataclass
class MotorConfig:
    """DC motor configuration"""
    pin_a: int
    pin_b: int
    enable_pin: int
    encoder_pin_a: Optional[int] = None
    encoder_pin_b: Optional[int] = None
    max_speed: float = 1.0

class HardwareConfig(BaseModel):
    """Hardware configuration model"""
    # Servo configurations
    servos: Dict[str, ServoConfig] = Field(default_factory=dict)
    
    # Motor configurations
    motors: Dict[str, MotorConfig] = Field(default_factory=dict)
    
    # Sensor pins
    camera_index: int = 0
    microphone_index: int = 0
    speaker_pin: int = 18
    
    # I2C devices
    i2c_bus: int = 1
    mpu6050_address: int = 0x68
    
    # GPIO pins
    led_pin: int = 25
    button_pin: int = 24
    ultrasonic_trigger: int = 23
    ultrasonic_echo: int = 22

class AIConfig(BaseModel):
    """AI and ML configuration"""
    # Face recognition
    face_recognition_tolerance: float = 0.6
    face_detection_model: str = "hog"  # or "cnn"
    
    # Voice recognition
    voice_recognition_timeout: float = 5.0
    voice_recognition_phrase_timeout: float = 0.3
    
    # Text-to-speech
    tts_rate: int = 150
    tts_volume: float = 0.9
    tts_voice: str = "english"
    
    # Computer vision
    cv_frame_width: int = 640
    cv_frame_height: int = 480
    cv_fps: int = 30
    
    # Model paths
    face_model_path: str = "models/face_recognition"
    gesture_model_path: str = "models/gesture_recognition"
    object_model_path: str = "models/object_detection"

class NetworkConfig(BaseModel):
    """Network and communication configuration"""
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    websocket_port: int = 8001
    
    # Mobile app settings
    mobile_app_port: int = 3000
    
    # Security
    api_key: Optional[str] = None
    cors_origins: list = ["*"]

class RobotConfig(BaseModel):
    """Robot behavior configuration"""
    # Movement parameters
    max_linear_speed: float = 0.5  # m/s
    max_angular_speed: float = 1.0  # rad/s
    acceleration_limit: float = 0.2  # m/s²
    
    # Servo movement
    servo_update_rate: float = 50.0  # Hz
    movement_smoothing: bool = True
    
    # Behavior settings
    idle_timeout: float = 300.0  # seconds
    auto_sleep: bool = True
    
    # Safety limits
    collision_distance: float = 0.2  # meters
    emergency_stop_enabled: bool = True

class Config:
    """Main configuration class"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or self._find_config_file()
        self.hardware = HardwareConfig()
        self.ai = AIConfig()
        self.network = NetworkConfig()
        self.robot = RobotConfig()
        
        # Load configuration
        self._load_config()
        self._setup_hardware_defaults()
        self._load_environment_variables()
    
    def _find_config_file(self) -> str:
        """Find configuration file"""
        possible_paths = [
            "config/robot_config.yaml",
            "../config/robot_config.yaml",
            "robot_config.yaml"
        ]
        
        for path in possible_paths:
            if Path(path).exists():
                return path
        
        # Create default config if none found
        return self._create_default_config()
    
    def _create_default_config(self) -> str:
        """Create default configuration file"""
        config_dir = Path("config")
        config_dir.mkdir(exist_ok=True)
        
        config_path = config_dir / "robot_config.yaml"
        default_config = {
            "hardware": {
                "servos": {
                    "neck": {"pin": 0, "default_angle": 90},
                    "left_shoulder": {"pin": 1, "default_angle": 90},
                    "left_elbow": {"pin": 2, "default_angle": 90},
                    "right_shoulder": {"pin": 3, "default_angle": 90},
                    "right_elbow": {"pin": 4, "default_angle": 90}
                },
                "motors": {
                    "left_wheel": {"pin_a": 5, "pin_b": 6, "enable_pin": 7},
                    "right_wheel": {"pin_a": 8, "pin_b": 9, "enable_pin": 10}
                }
            }
        }
        
        with open(config_path, 'w') as f:
            yaml.dump(default_config, f, default_flow_style=False)
        
        return str(config_path)
    
    def _load_config(self):
        """Load configuration from file"""
        if not Path(self.config_file).exists():
            return
        
        try:
            with open(self.config_file, 'r') as f:
                config_data = yaml.safe_load(f)
            
            if config_data:
                # Update configurations
                if 'hardware' in config_data:
                    self._update_hardware_config(config_data['hardware'])
                if 'ai' in config_data:
                    self.ai = AIConfig(**config_data['ai'])
                if 'network' in config_data:
                    self.network = NetworkConfig(**config_data['network'])
                if 'robot' in config_data:
                    self.robot = RobotConfig(**config_data['robot'])
                    
        except Exception as e:
            print(f"Error loading config: {e}")
    
    def _update_hardware_config(self, hardware_data: Dict[str, Any]):
        """Update hardware configuration"""
        if 'servos' in hardware_data:
            for name, servo_data in hardware_data['servos'].items():
                self.hardware.servos[name] = ServoConfig(**servo_data)
        
        if 'motors' in hardware_data:
            for name, motor_data in hardware_data['motors'].items():
                self.hardware.motors[name] = MotorConfig(**motor_data)
    
    def _setup_hardware_defaults(self):
        """Setup default hardware configuration"""
        if not self.hardware.servos:
            # Default servo configuration
            default_servos = {
                "neck": ServoConfig(pin=0, default_angle=90),
                "left_shoulder": ServoConfig(pin=1, default_angle=90),
                "left_elbow": ServoConfig(pin=2, default_angle=90),
                "right_shoulder": ServoConfig(pin=3, default_angle=90),
                "right_elbow": ServoConfig(pin=4, default_angle=90)
            }
            self.hardware.servos.update(default_servos)
        
        if not self.hardware.motors:
            # Default motor configuration
            default_motors = {
                "left_wheel": MotorConfig(pin_a=5, pin_b=6, enable_pin=7),
                "right_wheel": MotorConfig(pin_a=8, pin_b=9, enable_pin=10)
            }
            self.hardware.motors.update(default_motors)
    
    def _load_environment_variables(self):
        """Load configuration from environment variables"""
        # Network settings
        if os.getenv('API_HOST'):
            self.network.api_host = os.getenv('API_HOST')
        if os.getenv('API_PORT'):
            self.network.api_port = int(os.getenv('API_PORT'))
        
        # AI settings
        if os.getenv('FACE_RECOGNITION_TOLERANCE'):
            self.ai.face_recognition_tolerance = float(os.getenv('FACE_RECOGNITION_TOLERANCE'))
    
    def save_config(self):
        """Save current configuration to file"""
        config_data = {
            'hardware': {
                'servos': {name: servo.__dict__ for name, servo in self.hardware.servos.items()},
                'motors': {name: motor.__dict__ for name, motor in self.hardware.motors.items()},
                'camera_index': self.hardware.camera_index,
                'microphone_index': self.hardware.microphone_index
            },
            'ai': self.ai.dict(),
            'network': self.network.dict(),
            'robot': self.robot.dict()
        }
        
        with open(self.config_file, 'w') as f:
            yaml.dump(config_data, f, default_flow_style=False)
    
    def get_servo_config(self, servo_name: str) -> Optional[ServoConfig]:
        """Get servo configuration by name"""
        return self.hardware.servos.get(servo_name)
    
    def get_motor_config(self, motor_name: str) -> Optional[MotorConfig]:
        """Get motor configuration by name"""
        return self.hardware.motors.get(motor_name)
