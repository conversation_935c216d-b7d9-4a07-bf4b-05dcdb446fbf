# STEM_XPERT Robot - Alpha 1E Clone

A comprehensive educational humanoid robot project inspired by UBTech Alpha 1E, featuring advanced AI capabilities, voice interaction, facial recognition, and visual programming.

## 🤖 Features

### Core Capabilities
- **Visual Programming**: Blockly-based coding interface identical to Alpha 1E
- **Voice Interaction**: Advanced speech recognition and text-to-speech
- **Facial Recognition**: Real-time face detection and tracking
- **Servo Motor Control**: Precise 5-servo movement system
- **Wheel-based Movement**: Differential drive locomotion
- **Educational Platform**: Comprehensive STEM learning modules
- **Dance & Entertainment**: Pre-programmed movement sequences
- **Sensor Integration**: Multi-sensor data fusion and monitoring
- **Real-time Control**: WebSocket-based live robot control
- **Mobile Interface**: Cross-platform web and mobile access

### Hardware Configuration
- **Arms**: 2 servo motors per arm (shoulder, elbow) - 4 total
- **Movement**: Differential drive wheel system with encoders
- **Head**: 1 servo motor for neck rotation and face tracking
- **Sensors**: Camera, dual microphones, IMU (MPU6050), ultrasonic
- **Processing**: Raspberry Pi 4 with ARM Cortex-A72 (or any compatible system)
- **Audio**: 3W speaker system with voice synthesis
- **Power**: Intelligent battery management with monitoring

## 🏗️ Architecture

```
STEM_XPERT Robot/
├── backend/                 # Python FastAPI backend
│   ├── core/               # Robot controller and configuration
│   ├── ai/                 # AI engines (voice, vision, ML)
│   ├── hardware/           # Hardware abstraction layer
│   └── api/                # REST API and WebSocket endpoints
├── frontend/               # React.js web interface
│   ├── components/         # UI components
│   │   ├── Dashboard/      # Robot status and monitoring
│   │   ├── Blockly/        # Visual programming interface
│   │   ├── RobotControl/   # Manual control panel
│   │   ├── Voice/          # Voice control interface
│   │   ├── Face/           # Face recognition panel
│   │   └── Education/      # Learning modules
│   └── services/           # API and WebSocket services
├── config/                 # Configuration files
├── scripts/                # Startup and utility scripts
├── models/                 # AI model storage
└── docs/                   # Documentation
```

## 🚀 Quick Start

### Windows Installation

1. **Install Dependencies**
   ```cmd
   scripts\install_dependencies.bat
   ```

2. **Start Robot**
   ```cmd
   scripts\start_robot.bat
   ```

3. **Access Interface**
   - Open browser to: http://localhost:8000
   - Dashboard, programming, and control interfaces available

### Linux/macOS Installation

1. **Install Dependencies**
   ```bash
   chmod +x scripts/*.sh
   ./scripts/install_dependencies.sh
   ```

2. **Start Robot**
   ```bash
   ./scripts/start_robot.sh
   ```

3. **Stop Robot**
   ```bash
   ./scripts/stop_robot.sh
   ```

## 📋 Requirements

### Hardware Requirements
- **Microcontroller**: Raspberry Pi 4 (4GB+ recommended) or compatible
- **Servos**: 5x Digital servo motors (MG996R or similar)
  - 1x Neck servo (pan/tilt head movement)
  - 2x Left arm servos (shoulder, elbow)
  - 2x Right arm servos (shoulder, elbow)
- **Motors**: 2x DC motors with encoders for wheel movement
- **Sensors**:
  - Camera module (Pi Camera V2 or USB webcam)
  - 2x USB microphones or I2S microphone array
  - MPU6050 IMU sensor (accelerometer + gyroscope)
  - HC-SR04 ultrasonic distance sensor
  - Optional: Additional sensors for advanced features
- **Audio**: 3W+ speaker or amplified speaker system
- **Power**: 7.4V LiPo battery with power management board
- **Connectivity**: WiFi module (built-in on Pi 4)

### Software Requirements
- **Operating System**:
  - Raspberry Pi OS (recommended for Pi)
  - Ubuntu 20.04+ (for other systems)
  - Windows 10+ (development/simulation)
  - macOS 10.15+ (development/simulation)
- **Python**: 3.8 or higher
- **Node.js**: 16.0 or higher (for frontend)
- **Libraries**: All dependencies auto-installed via scripts

### Minimum System Specifications
- **RAM**: 4GB (8GB recommended)
- **Storage**: 32GB microSD card (64GB+ recommended)
- **CPU**: ARM Cortex-A72 (Pi 4) or equivalent x86_64
- **GPU**: Hardware acceleration for computer vision (optional)

## 🎯 Educational Goals

### Programming Concepts
- **Visual Programming**: Drag-and-drop Blockly interface
- **Variables and Data Types**: Understanding robot state and sensor data
- **Control Structures**: Loops, conditionals, and functions
- **Event-Driven Programming**: Responding to sensors and user input
- **Debugging**: Real-time program monitoring and error handling

### Robotics Fundamentals
- **Kinematics**: Understanding servo movement and positioning
- **Sensor Integration**: Reading and processing sensor data
- **Actuator Control**: Precise motor and servo control
- **Feedback Systems**: Closed-loop control with encoders
- **Robot Behavior**: Programming autonomous and reactive behaviors

### AI & Machine Learning
- **Computer Vision**: Face detection and recognition
- **Speech Processing**: Voice recognition and synthesis
- **Machine Learning**: Pattern recognition and classification
- **Sensor Fusion**: Combining multiple sensor inputs
- **Real-time Processing**: Handling continuous data streams

### STEM Integration
- **Mathematics**: Trigonometry, algebra, and coordinate systems
- **Physics**: Mechanics, electronics, and signal processing
- **Engineering**: System design and problem-solving
- **Computer Science**: Algorithms, data structures, and software design

## 🎓 Educational Features

### Interactive Tutorials
- **Getting Started**: Basic robot operation and safety
- **First Program**: Creating simple movement sequences
- **Sensor Exploration**: Understanding robot perception
- **Advanced Programming**: Complex behaviors and AI integration

### Hands-on Challenges
- **Movement Challenges**: Navigate obstacles and follow paths
- **Programming Puzzles**: Solve problems using visual programming
- **AI Projects**: Implement face tracking and voice commands
- **Creative Expression**: Design custom dances and behaviors

### Assessment Tools
- **Progress Tracking**: Monitor learning objectives and milestones
- **Skill Validation**: Practical programming and robotics assessments
- **Portfolio Building**: Document projects and achievements
- **Peer Collaboration**: Share programs and learn from others

## 📖 Documentation

- [Hardware Assembly Guide](docs/hardware.md) - Complete build instructions
- [Software Installation](docs/installation.md) - Detailed setup procedures
- [Programming Tutorials](docs/tutorials.md) - Step-by-step learning modules
- [API Reference](docs/api.md) - Complete API documentation
- [Troubleshooting Guide](docs/troubleshooting.md) - Common issues and solutions
- [Advanced Features](docs/advanced.md) - Custom development and extensions

## 🔧 Technical Specifications

### Performance Characteristics
- **Movement Speed**: Up to 0.5 m/s linear, 1.0 rad/s angular
- **Servo Precision**: ±1 degree accuracy
- **Response Time**: <100ms for basic commands
- **Battery Life**: 2-4 hours continuous operation
- **Operating Range**: 10m WiFi range for remote control

### Communication Protocols
- **REST API**: HTTP/HTTPS for standard operations
- **WebSocket**: Real-time bidirectional communication
- **I2C**: Sensor communication bus
- **PWM**: Servo and motor control signals
- **GPIO**: Digital I/O for sensors and indicators

### Safety Features
- **Emergency Stop**: Immediate halt of all movement
- **Collision Detection**: Ultrasonic obstacle avoidance
- **Battery Monitoring**: Low power warnings and auto-shutdown
- **Thermal Protection**: Temperature monitoring and throttling
- **Safe Limits**: Software-enforced movement boundaries

## 🤝 Contributing

We welcome contributions from educators, students, and robotics enthusiasts!

### How to Contribute
1. **Fork the Repository**: Create your own copy of the project
2. **Create Feature Branch**: `git checkout -b feature/amazing-feature`
3. **Make Changes**: Implement your improvements or fixes
4. **Test Thoroughly**: Ensure all functionality works correctly
5. **Submit Pull Request**: Describe your changes and their benefits

### Contribution Areas
- **Educational Content**: Tutorials, challenges, and learning materials
- **Hardware Support**: Additional sensor and actuator integrations
- **AI Features**: Enhanced computer vision and machine learning
- **User Interface**: Improved web and mobile interfaces
- **Documentation**: Better guides, examples, and explanations
- **Testing**: Bug reports, test cases, and quality assurance

## 📄 License

This project is licensed under the MIT License - see [LICENSE](LICENSE) for details.

### Open Source Components
- **FastAPI**: Modern Python web framework
- **React**: User interface library
- **Blockly**: Visual programming editor
- **OpenCV**: Computer vision library
- **TensorFlow**: Machine learning framework

## 🙏 Acknowledgments

- **Inspiration**: UBTech Alpha 1E humanoid robot
- **Educational Focus**: Designed for STEM learning and robotics education
- **Community**: Built with and for the maker and education communities
- **Open Source**: Leveraging the power of open-source technologies

## 🆘 Support

### Getting Help
- **Documentation**: Check the comprehensive docs/ directory
- **Issues**: Report bugs and request features on GitHub
- **Discussions**: Join community discussions and Q&A
- **Wiki**: Community-maintained knowledge base

### Community Resources
- **Discord Server**: Real-time chat and support
- **YouTube Channel**: Video tutorials and demonstrations
- **Blog**: Project updates and educational articles
- **Newsletter**: Monthly updates and featured projects

---

**Ready to start your robotics journey? Install the dependencies and launch your STEM_XPERT robot today!**
