import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { Box, Alert, Snackbar } from '@mui/material';

// Import components
import Navigation from './components/Navigation/Navigation';
import Dashboard from './components/Dashboard/Dashboard';
import RobotControl from './components/RobotControl/RobotControl';
import BlocklyProgramming from './components/Blockly/BlocklyProgramming';
import VoiceControl from './components/Voice/VoiceControl';
import FaceRecognition from './components/Face/FaceRecognition';
import Settings from './components/Settings/Settings';
import Education from './components/Education/Education';

// Import services
import { RobotAPI } from './services/RobotAPI';
import { WebSocketService } from './services/WebSocketService';

// Create theme
const theme = createTheme({
  palette: {
    mode: 'dark',
    primary: {
      main: '#667eea',
    },
    secondary: {
      main: '#764ba2',
    },
    background: {
      default: '#1a1a1a',
      paper: '#2d2d2d',
    },
  },
  typography: {
    fontFamily: 'Roboto, Arial, sans-serif',
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 500,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 8,
          textTransform: 'none',
          fontWeight: 500,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: 12,
          boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
        },
      },
    },
  },
});

function App() {
  // Application state
  const [robotStatus, setRobotStatus] = useState({
    connected: false,
    state: 'disconnected',
    battery: 0,
    uptime: 0
  });
  
  const [notifications, setNotifications] = useState([]);
  const [currentNotification, setCurrentNotification] = useState(null);
  
  // Services
  const [robotAPI] = useState(new RobotAPI());
  const [wsService] = useState(new WebSocketService());
  
  // Initialize services
  useEffect(() => {
    initializeServices();
    
    return () => {
      wsService.disconnect();
    };
  }, []);
  
  // Handle notifications
  useEffect(() => {
    if (notifications.length > 0 && !currentNotification) {
      setCurrentNotification(notifications[0]);
      setNotifications(prev => prev.slice(1));
    }
  }, [notifications, currentNotification]);
  
  const initializeServices = async () => {
    try {
      // Initialize WebSocket connection
      await wsService.connect();
      
      // Set up event handlers
      wsService.on('status_update', handleStatusUpdate);
      wsService.on('connection_status', handleConnectionStatus);
      wsService.on('robot_event', handleRobotEvent);
      wsService.on('error', handleError);
      
      // Subscribe to monitoring updates
      wsService.subscribe(['monitoring', 'web']);
      
      // Get initial status
      const status = await robotAPI.getStatus();
      if (status.success) {
        setRobotStatus(prev => ({
          ...prev,
          ...status.data.robot,
          connected: true
        }));
      }
      
      addNotification('Connected to robot successfully', 'success');
      
    } catch (error) {
      console.error('Failed to initialize services:', error);
      addNotification('Failed to connect to robot', 'error');
    }
  };
  
  const handleStatusUpdate = (data) => {
    setRobotStatus(prev => ({
      ...prev,
      ...data,
      connected: true
    }));
    
    // Update global status indicator
    if (window.updateRobotStatus) {
      window.updateRobotStatus('connected');
    }
  };
  
  const handleConnectionStatus = (data) => {
    const isConnected = data.status === 'connected';
    setRobotStatus(prev => ({
      ...prev,
      connected: isConnected
    }));
    
    // Update global status indicator
    if (window.updateRobotStatus) {
      window.updateRobotStatus(data.status);
    }
    
    if (isConnected) {
      addNotification('Robot connected', 'success');
    } else {
      addNotification('Robot disconnected', 'warning');
    }
  };
  
  const handleRobotEvent = (data) => {
    console.log('Robot event:', data);
    
    // Handle specific robot events
    switch (data.type) {
      case 'command_executed':
        addNotification(`Command executed: ${data.command.type}`, 'info');
        break;
      case 'program_started':
        addNotification(`Program started: ${data.program_name}`, 'info');
        break;
      case 'voice_command_processed':
        addNotification(`Voice command: "${data.command}"`, 'info');
        break;
      case 'face_detected':
        addNotification('Face detected', 'info');
        break;
      default:
        break;
    }
  };
  
  const handleError = (error) => {
    console.error('WebSocket error:', error);
    addNotification(`Error: ${error.message}`, 'error');
  };
  
  const addNotification = (message, severity = 'info') => {
    const notification = {
      id: Date.now(),
      message,
      severity,
      timestamp: new Date()
    };
    
    setNotifications(prev => [...prev, notification]);
  };
  
  const handleCloseNotification = () => {
    setCurrentNotification(null);
  };
  
  const sendRobotCommand = async (command) => {
    try {
      // Send via WebSocket for real-time response
      wsService.send({
        type: 'robot_command',
        command: command,
        command_id: Date.now()
      });
      
      return { success: true };
    } catch (error) {
      console.error('Error sending robot command:', error);
      addNotification('Failed to send command', 'error');
      return { success: false, error: error.message };
    }
  };
  
  // App context for child components
  const appContext = {
    robotStatus,
    robotAPI,
    wsService,
    sendRobotCommand,
    addNotification
  };
  
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Router>
        <Box sx={{ display: 'flex', minHeight: '100vh' }}>
          {/* Navigation Sidebar */}
          <Navigation robotStatus={robotStatus} />
          
          {/* Main Content */}
          <Box
            component="main"
            sx={{
              flexGrow: 1,
              p: 3,
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              minHeight: '100vh'
            }}
          >
            <Routes>
              <Route 
                path="/" 
                element={<Navigate to="/dashboard" replace />} 
              />
              <Route 
                path="/dashboard" 
                element={<Dashboard {...appContext} />} 
              />
              <Route 
                path="/control" 
                element={<RobotControl {...appContext} />} 
              />
              <Route 
                path="/programming" 
                element={<BlocklyProgramming {...appContext} />} 
              />
              <Route 
                path="/voice" 
                element={<VoiceControl {...appContext} />} 
              />
              <Route 
                path="/face" 
                element={<FaceRecognition {...appContext} />} 
              />
              <Route 
                path="/education" 
                element={<Education {...appContext} />} 
              />
              <Route 
                path="/settings" 
                element={<Settings {...appContext} />} 
              />
            </Routes>
          </Box>
        </Box>
        
        {/* Notification Snackbar */}
        <Snackbar
          open={!!currentNotification}
          autoHideDuration={4000}
          onClose={handleCloseNotification}
          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}
        >
          {currentNotification && (
            <Alert
              onClose={handleCloseNotification}
              severity={currentNotification.severity}
              variant="filled"
              sx={{ width: '100%' }}
            >
              {currentNotification.message}
            </Alert>
          )}
        </Snackbar>
      </Router>
    </ThemeProvider>
  );
}

export default App;
