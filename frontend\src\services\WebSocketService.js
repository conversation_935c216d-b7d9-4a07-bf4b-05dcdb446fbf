/**
 * WebSocket Service - Real-time communication with robot
 * Handles WebSocket connection and message routing
 */

import { io } from 'socket.io-client';

export class WebSocketService {
  constructor() {
    this.socket = null;
    this.connected = false;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;
    this.eventHandlers = new Map();
    this.subscriptions = new Set();
    
    // Message queue for offline messages
    this.messageQueue = [];
    this.queueEnabled = true;
  }
  
  async connect(url = null) {
    try {
      // Determine WebSocket URL
      const wsUrl = url || this._getWebSocketURL();
      
      // Create WebSocket connection
      this.socket = new WebSocket(wsUrl);
      
      // Set up event handlers
      this.socket.onopen = this._handleOpen.bind(this);
      this.socket.onmessage = this._handleMessage.bind(this);
      this.socket.onclose = this._handleClose.bind(this);
      this.socket.onerror = this._handleError.bind(this);
      
      // Wait for connection
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket connection timeout'));
        }, 10000);
        
        this.socket.onopen = () => {
          clearTimeout(timeout);
          this._handleOpen();
          resolve();
        };
        
        this.socket.onerror = (error) => {
          clearTimeout(timeout);
          reject(error);
        };
      });
      
    } catch (error) {
      console.error('Failed to connect WebSocket:', error);
      throw error;
    }
  }
  
  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
    this.connected = false;
    this.subscriptions.clear();
  }
  
  _getWebSocketURL() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const host = window.location.host;
    return `${protocol}//${host}/ws`;
  }
  
  _handleOpen() {
    console.log('WebSocket connected');
    this.connected = true;
    this.reconnectAttempts = 0;
    
    // Send queued messages
    this._sendQueuedMessages();
    
    // Re-subscribe to previous subscriptions
    if (this.subscriptions.size > 0) {
      this.subscribe(Array.from(this.subscriptions));
    }
    
    // Start ping interval
    this._startPingInterval();
    
    // Emit connection event
    this._emit('connection_status', { status: 'connected' });
  }
  
  _handleMessage(event) {
    try {
      const data = JSON.parse(event.data);
      this._processMessage(data);
    } catch (error) {
      console.error('Error parsing WebSocket message:', error);
    }
  }
  
  _handleClose(event) {
    console.log('WebSocket disconnected:', event.code, event.reason);
    this.connected = false;
    
    // Stop ping interval
    this._stopPingInterval();
    
    // Emit disconnection event
    this._emit('connection_status', { status: 'disconnected' });
    
    // Attempt reconnection
    if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
      this._attemptReconnect();
    }
  }
  
  _handleError(error) {
    console.error('WebSocket error:', error);
    this._emit('error', { message: 'WebSocket connection error', error });
  }
  
  _processMessage(data) {
    const { type, ...payload } = data;
    
    switch (type) {
      case 'connection_established':
        console.log('Connection established:', payload.client_id);
        break;
        
      case 'pong':
        // Handle ping response
        break;
        
      case 'status_update':
        this._emit('status_update', payload.data);
        break;
        
      case 'command_accepted':
        this._emit('command_response', payload);
        break;
        
      case 'command_executed':
        this._emit('robot_event', { type: 'command_executed', ...payload });
        break;
        
      case 'program_started':
        this._emit('robot_event', { type: 'program_started', ...payload });
        break;
        
      case 'voice_command_processed':
        this._emit('robot_event', { type: 'voice_command_processed', ...payload });
        break;
        
      case 'face_detected':
        this._emit('robot_event', { type: 'face_detected', ...payload });
        break;
        
      case 'error':
        this._emit('error', payload);
        break;
        
      default:
        console.log('Unknown message type:', type, payload);
        this._emit('message', data);
        break;
    }
  }
  
  _attemptReconnect() {
    this.reconnectAttempts++;
    const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
    
    console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`);
    
    this._emit('connection_status', { status: 'connecting' });
    
    setTimeout(async () => {
      try {
        await this.connect();
      } catch (error) {
        console.error('Reconnection failed:', error);
        
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
          console.error('Max reconnection attempts reached');
          this._emit('connection_status', { status: 'failed' });
        }
      }
    }, delay);
  }
  
  _startPingInterval() {
    this.pingInterval = setInterval(() => {
      if (this.connected) {
        this.send({ type: 'ping', timestamp: Date.now() });
      }
    }, 30000); // Ping every 30 seconds
  }
  
  _stopPingInterval() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval);
      this.pingInterval = null;
    }
  }
  
  send(data) {
    if (this.connected && this.socket.readyState === WebSocket.OPEN) {
      try {
        this.socket.send(JSON.stringify(data));
        return true;
      } catch (error) {
        console.error('Error sending WebSocket message:', error);
        return false;
      }
    } else {
      // Queue message if not connected
      if (this.queueEnabled) {
        this.messageQueue.push(data);
        console.log('Message queued (not connected):', data.type);
      }
      return false;
    }
  }
  
  _sendQueuedMessages() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift();
      this.send(message);
    }
  }
  
  subscribe(groups) {
    if (!Array.isArray(groups)) {
      groups = [groups];
    }
    
    groups.forEach(group => this.subscriptions.add(group));
    
    return this.send({
      type: 'subscribe',
      groups: groups
    });
  }
  
  unsubscribe(groups) {
    if (!Array.isArray(groups)) {
      groups = [groups];
    }
    
    groups.forEach(group => this.subscriptions.delete(group));
    
    return this.send({
      type: 'unsubscribe',
      groups: groups
    });
  }
  
  // Event handling
  on(event, handler) {
    if (!this.eventHandlers.has(event)) {
      this.eventHandlers.set(event, new Set());
    }
    this.eventHandlers.get(event).add(handler);
  }
  
  off(event, handler) {
    if (this.eventHandlers.has(event)) {
      this.eventHandlers.get(event).delete(handler);
    }
  }
  
  _emit(event, data) {
    if (this.eventHandlers.has(event)) {
      this.eventHandlers.get(event).forEach(handler => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error in event handler for ${event}:`, error);
        }
      });
    }
  }
  
  // Robot-specific methods
  sendRobotCommand(command) {
    return this.send({
      type: 'robot_command',
      command: command,
      command_id: Date.now()
    });
  }
  
  requestStatus() {
    return this.send({
      type: 'get_status',
      timestamp: Date.now()
    });
  }
  
  executeBlocklyProgram(code, name = 'Untitled') {
    return this.send({
      type: 'blockly_execute',
      code: code,
      name: name,
      timestamp: Date.now()
    });
  }
  
  sendVoiceCommand(text) {
    return this.send({
      type: 'voice_command',
      text: text,
      timestamp: Date.now()
    });
  }
  
  // Utility methods
  isConnected() {
    return this.connected && this.socket && this.socket.readyState === WebSocket.OPEN;
  }
  
  getConnectionState() {
    if (!this.socket) return 'disconnected';
    
    switch (this.socket.readyState) {
      case WebSocket.CONNECTING:
        return 'connecting';
      case WebSocket.OPEN:
        return 'connected';
      case WebSocket.CLOSING:
        return 'disconnecting';
      case WebSocket.CLOSED:
        return 'disconnected';
      default:
        return 'unknown';
    }
  }
  
  enableMessageQueue(enabled = true) {
    this.queueEnabled = enabled;
    if (!enabled) {
      this.messageQueue = [];
    }
  }
  
  getQueuedMessageCount() {
    return this.messageQueue.length;
  }
  
  clearMessageQueue() {
    this.messageQueue = [];
  }
}
