import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON>graphy,
  Grid,
  TextField,
  Switch,
  FormControlLabel,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>
} from '@mui/material';
import {
  Save,
  RestoreFromTrash,
  Settings as SettingsIcon
} from '@mui/icons-material';

function Settings({ robotAPI, addNotification }) {
  const [settings, setSettings] = useState({
    // Robot behavior
    maxSpeed: 0.5,
    autoSleep: true,
    idleTimeout: 300,
    
    // Voice settings
    voiceEnabled: true,
    ttsRate: 150,
    ttsVolume: 0.9,
    
    // Face recognition
    faceRecognitionEnabled: true,
    faceTrackingEnabled: true,
    recognitionTolerance: 0.6,
    
    // Network settings
    apiHost: '0.0.0.0',
    apiPort: 8000,
    
    // Safety settings
    emergencyStopEnabled: true,
    collisionDistance: 0.2
  });
  
  const [hasChanges, setHasChanges] = useState(false);
  
  const handleSettingChange = (key, value) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }));
    setHasChanges(true);
  };
  
  const saveSettings = async () => {
    try {
      // In a real implementation, this would save to the robot
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      setHasChanges(false);
      addNotification('Settings saved successfully', 'success');
    } catch (error) {
      addNotification('Failed to save settings', 'error');
    }
  };
  
  const resetSettings = () => {
    setSettings({
      maxSpeed: 0.5,
      autoSleep: true,
      idleTimeout: 300,
      voiceEnabled: true,
      ttsRate: 150,
      ttsVolume: 0.9,
      faceRecognitionEnabled: true,
      faceTrackingEnabled: true,
      recognitionTolerance: 0.6,
      apiHost: '0.0.0.0',
      apiPort: 8000,
      emergencyStopEnabled: true,
      collisionDistance: 0.2
    });
    setHasChanges(true);
    addNotification('Settings reset to defaults', 'info');
  };
  
  return (
    <Box>
      <Typography variant="h4" sx={{ fontWeight: 600, color: 'white', mb: 3 }}>
        Robot Settings
      </Typography>
      
      {hasChanges && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          You have unsaved changes. Don't forget to save your settings.
        </Alert>
      )}
      
      <Grid container spacing={3}>
        {/* Robot Behavior */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Robot Behavior
              </Typography>
              
              <Box sx={{ mb: 3 }}>
                <Typography gutterBottom>
                  Maximum Speed: {settings.maxSpeed.toFixed(1)}
                </Typography>
                <Slider
                  value={settings.maxSpeed}
                  onChange={(e, value) => handleSettingChange('maxSpeed', value)}
                  min={0.1}
                  max={1.0}
                  step={0.1}
                  marks
                  valueLabelDisplay="auto"
                />
              </Box>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.autoSleep}
                    onChange={(e) => handleSettingChange('autoSleep', e.target.checked)}
                  />
                }
                label="Auto Sleep Mode"
                sx={{ mb: 2 }}
              />
              
              <TextField
                fullWidth
                label="Idle Timeout (seconds)"
                type="number"
                value={settings.idleTimeout}
                onChange={(e) => handleSettingChange('idleTimeout', parseInt(e.target.value))}
                sx={{ mb: 2 }}
              />
            </CardContent>
          </Card>
        </Grid>
        
        {/* Voice Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Voice Settings
              </Typography>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.voiceEnabled}
                    onChange={(e) => handleSettingChange('voiceEnabled', e.target.checked)}
                  />
                }
                label="Voice Recognition Enabled"
                sx={{ mb: 2 }}
              />
              
              <Box sx={{ mb: 3 }}>
                <Typography gutterBottom>
                  TTS Rate: {settings.ttsRate}
                </Typography>
                <Slider
                  value={settings.ttsRate}
                  onChange={(e, value) => handleSettingChange('ttsRate', value)}
                  min={50}
                  max={300}
                  step={10}
                  valueLabelDisplay="auto"
                />
              </Box>
              
              <Box sx={{ mb: 2 }}>
                <Typography gutterBottom>
                  TTS Volume: {settings.ttsVolume.toFixed(1)}
                </Typography>
                <Slider
                  value={settings.ttsVolume}
                  onChange={(e, value) => handleSettingChange('ttsVolume', value)}
                  min={0.0}
                  max={1.0}
                  step={0.1}
                  valueLabelDisplay="auto"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Face Recognition */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Face Recognition
              </Typography>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.faceRecognitionEnabled}
                    onChange={(e) => handleSettingChange('faceRecognitionEnabled', e.target.checked)}
                  />
                }
                label="Face Recognition Enabled"
                sx={{ mb: 2 }}
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.faceTrackingEnabled}
                    onChange={(e) => handleSettingChange('faceTrackingEnabled', e.target.checked)}
                    disabled={!settings.faceRecognitionEnabled}
                  />
                }
                label="Face Tracking Enabled"
                sx={{ mb: 3 }}
              />
              
              <Box sx={{ mb: 2 }}>
                <Typography gutterBottom>
                  Recognition Tolerance: {settings.recognitionTolerance.toFixed(1)}
                </Typography>
                <Slider
                  value={settings.recognitionTolerance}
                  onChange={(e, value) => handleSettingChange('recognitionTolerance', value)}
                  min={0.1}
                  max={1.0}
                  step={0.1}
                  valueLabelDisplay="auto"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Safety Settings */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Safety Settings
              </Typography>
              
              <FormControlLabel
                control={
                  <Switch
                    checked={settings.emergencyStopEnabled}
                    onChange={(e) => handleSettingChange('emergencyStopEnabled', e.target.checked)}
                  />
                }
                label="Emergency Stop Enabled"
                sx={{ mb: 3 }}
              />
              
              <Box sx={{ mb: 2 }}>
                <Typography gutterBottom>
                  Collision Distance: {settings.collisionDistance.toFixed(1)}m
                </Typography>
                <Slider
                  value={settings.collisionDistance}
                  onChange={(e, value) => handleSettingChange('collisionDistance', value)}
                  min={0.1}
                  max={1.0}
                  step={0.1}
                  valueLabelDisplay="auto"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Network Settings */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Network Settings
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="API Host"
                    value={settings.apiHost}
                    onChange={(e) => handleSettingChange('apiHost', e.target.value)}
                  />
                </Grid>
                
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    label="API Port"
                    type="number"
                    value={settings.apiPort}
                    onChange={(e) => handleSettingChange('apiPort', parseInt(e.target.value))}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Action Buttons */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
                <Button
                  variant="outlined"
                  startIcon={<RestoreFromTrash />}
                  onClick={resetSettings}
                >
                  Reset to Defaults
                </Button>
                
                <Button
                  variant="contained"
                  startIcon={<Save />}
                  onClick={saveSettings}
                  disabled={!hasChanges}
                >
                  Save Settings
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default Settings;
