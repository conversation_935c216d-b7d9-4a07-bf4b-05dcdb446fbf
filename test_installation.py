#!/usr/bin/env python3
"""
STEM_XPERT Robot Installation Test
This script tests if all components are properly installed and configured
"""

import sys
import os
import subprocess
import importlib
from pathlib import Path

def test_python_version():
    """Test Python version compatibility"""
    print("🐍 Testing Python version...")
    version = sys.version_info
    if version.major == 3 and version.minor >= 8:
        print(f"   ✓ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"   ✗ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False

def test_python_dependencies():
    """Test required Python packages"""
    print("\n📦 Testing Python dependencies...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'opencv-python',
        'numpy',
        'pyyaml',
        'asyncio'
    ]
    
    optional_packages = [
        'RPi.GPIO',
        'adafruit-circuitpython-servokit',
        'speech_recognition',
        'pyttsx3',
        'face_recognition'
    ]
    
    success = True
    
    for package in required_packages:
        try:
            importlib.import_module(package.replace('-', '_'))
            print(f"   ✓ {package}")
        except ImportError:
            print(f"   ✗ {package} - REQUIRED")
            success = False
    
    for package in optional_packages:
        try:
            importlib.import_module(package.replace('-', '_'))
            print(f"   ✓ {package} (optional)")
        except ImportError:
            print(f"   ⚠ {package} - Optional (hardware-specific)")
    
    return success

def test_project_structure():
    """Test project directory structure"""
    print("\n📁 Testing project structure...")
    
    required_dirs = [
        'backend',
        'frontend',
        'config',
        'scripts'
    ]
    
    required_files = [
        'requirements.txt',
        'backend/main.py',
        'backend/core/config.py',
        'backend/core/robot_controller.py',
        'config/robot_config.yaml'
    ]
    
    success = True
    
    for directory in required_dirs:
        if Path(directory).exists():
            print(f"   ✓ {directory}/")
        else:
            print(f"   ✗ {directory}/ - Missing directory")
            success = False
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"   ✓ {file_path}")
        else:
            print(f"   ✗ {file_path} - Missing file")
            success = False
    
    return success

def test_configuration():
    """Test configuration file"""
    print("\n⚙️ Testing configuration...")
    
    config_file = Path('config/robot_config.yaml')
    if not config_file.exists():
        print("   ✗ Configuration file not found")
        return False
    
    try:
        import yaml
        with open(config_file, 'r') as f:
            config = yaml.safe_load(f)
        
        required_sections = ['hardware', 'ai', 'network', 'robot']
        for section in required_sections:
            if section in config:
                print(f"   ✓ {section} section")
            else:
                print(f"   ✗ {section} section - Missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"   ✗ Configuration error: {e}")
        return False

def test_frontend():
    """Test frontend setup"""
    print("\n🌐 Testing frontend...")
    
    package_json = Path('frontend/package.json')
    if not package_json.exists():
        print("   ✗ frontend/package.json not found")
        return False
    
    print("   ✓ package.json found")
    
    node_modules = Path('frontend/node_modules')
    if node_modules.exists():
        print("   ✓ node_modules installed")
    else:
        print("   ⚠ node_modules not found - run 'npm install' in frontend/")
    
    build_dir = Path('frontend/build')
    if build_dir.exists():
        print("   ✓ Production build found")
    else:
        print("   ⚠ Production build not found - run 'npm run build' in frontend/")
    
    return True

def test_api_import():
    """Test if the main API can be imported"""
    print("\n🚀 Testing API import...")
    
    try:
        sys.path.insert(0, 'backend')
        from core.config import Config
        from core.robot_controller import RobotController
        from api.routes import router
        
        print("   ✓ Core modules imported successfully")
        
        # Test configuration loading
        config = Config()
        print("   ✓ Configuration loaded successfully")
        
        return True
        
    except Exception as e:
        print(f"   ✗ Import error: {e}")
        return False

def test_hardware_simulation():
    """Test hardware simulation mode"""
    print("\n🤖 Testing hardware simulation...")
    
    try:
        sys.path.insert(0, 'backend')
        from hardware.hardware_manager import HardwareManager
        from core.config import Config
        
        config = Config()
        hardware = HardwareManager(config)
        
        print("   ✓ Hardware manager created (simulation mode)")
        return True
        
    except Exception as e:
        print(f"   ✗ Hardware simulation error: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 STEM_XPERT Robot Installation Test")
    print("=" * 50)
    
    tests = [
        test_python_version,
        test_python_dependencies,
        test_project_structure,
        test_configuration,
        test_frontend,
        test_api_import,
        test_hardware_simulation
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"   ✗ Test failed with exception: {e}")
            results.append(False)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    if passed == total:
        print(f"🎉 All tests passed! ({passed}/{total})")
        print("\n✅ Your STEM_XPERT Robot installation is ready!")
        print("\n🚀 Next steps:")
        print("   1. Start the robot: python backend/main.py")
        print("   2. Open browser: http://localhost:8000")
        print("   3. Explore the dashboard and programming interface")
        return True
    else:
        print(f"⚠️  {passed}/{total} tests passed")
        print(f"❌ {total - passed} tests failed")
        print("\n🔧 Please fix the issues above before running the robot.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
