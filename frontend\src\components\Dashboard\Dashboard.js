import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Chip,
  IconButton,
  Button,
  Alert,
  Paper
} from '@mui/material';
import {
  SmartToy as RobotIcon,
  Battery90 as BatteryIcon,
  Thermostat as TempIcon,
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  Refresh as RefreshIcon,
  PlayArrow as PlayIcon,
  Stop as StopIcon,
  VolumeUp as SpeakIcon
} from '@mui/icons-material';
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

function Dashboard({ robotStatus, robotAPI, addNotification }) {
  const [sensorData, setSensorData] = useState(null);
  const [systemStats, setSystemStats] = useState([]);
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    loadDashboardData();
    
    // Set up periodic updates
    const interval = setInterval(loadDashboardData, 5000);
    return () => clearInterval(interval);
  }, []);
  
  const loadDashboardData = async () => {
    try {
      const [sensorResponse] = await Promise.all([
        robotAPI.getSensorData()
      ]);
      
      if (sensorResponse.success) {
        setSensorData(sensorResponse.data);
        
        // Add to system stats for chart
        const newStat = {
          time: new Date().toLocaleTimeString(),
          battery: robotStatus.battery || 0,
          temperature: sensorResponse.data.temperature || 25,
          distance: sensorResponse.data.ultrasonic_distance || 0
        };
        
        setSystemStats(prev => {
          const updated = [...prev, newStat];
          return updated.slice(-20); // Keep last 20 data points
        });
      }
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  };
  
  const handleRefresh = async () => {
    setLoading(true);
    try {
      await loadDashboardData();
      addNotification('Dashboard refreshed', 'success');
    } catch (error) {
      addNotification('Failed to refresh dashboard', 'error');
    } finally {
      setLoading(false);
    }
  };
  
  const handleQuickAction = async (action) => {
    try {
      switch (action) {
        case 'greet':
          await robotAPI.sayAndDo('Hello! I am STEM_XPERT robot. Nice to meet you!', 'wave');
          break;
        case 'demo':
          await robotAPI.performDance('celebrate');
          break;
        case 'stop':
          await robotAPI.stopRobot();
          break;
        default:
          break;
      }
      addNotification(`Quick action: ${action}`, 'success');
    } catch (error) {
      addNotification(`Failed to execute ${action}`, 'error');
    }
  };
  
  const getStatusColor = (state) => {
    const colors = {
      idle: 'success',
      moving: 'info',
      dancing: 'secondary',
      programming: 'warning',
      error: 'error',
      sleeping: 'default'
    };
    return colors[state] || 'default';
  };
  
  const formatUptime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    return `${hours}h ${minutes}m ${secs}s`;
  };
  
  return (
    <Box>
      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" sx={{ fontWeight: 600, color: 'white' }}>
          Robot Dashboard
        </Typography>
        <IconButton
          onClick={handleRefresh}
          disabled={loading}
          sx={{ color: 'white' }}
        >
          <RefreshIcon />
        </IconButton>
      </Box>
      
      {/* Connection Status Alert */}
      {!robotStatus.connected && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          Robot is not connected. Some features may not be available.
        </Alert>
      )}
      
      <Grid container spacing={3}>
        {/* Robot Status Card */}
        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <RobotIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">Robot Status</Typography>
              </Box>
              
              <Box sx={{ mb: 2 }}>
                <Chip
                  label={robotStatus.state || 'Unknown'}
                  color={getStatusColor(robotStatus.state)}
                  sx={{ mb: 1 }}
                />
              </Box>
              
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Uptime: {formatUptime(robotStatus.uptime || 0)}
              </Typography>
              
              <Typography variant="body2" color="text.secondary">
                Last Activity: {robotStatus.last_activity ? 
                  new Date(robotStatus.last_activity * 1000).toLocaleTimeString() : 
                  'Never'
                }
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Battery Status Card */}
        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <BatteryIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">Battery</Typography>
              </Box>
              
              <Box sx={{ mb: 1 }}>
                <Typography variant="h4" sx={{ fontWeight: 600 }}>
                  {Math.round(robotStatus.battery || 0)}%
                </Typography>
              </Box>
              
              <LinearProgress
                variant="determinate"
                value={robotStatus.battery || 0}
                sx={{ mb: 1, height: 8, borderRadius: 4 }}
                color={robotStatus.battery > 30 ? 'success' : 'error'}
              />
              
              <Typography variant="body2" color="text.secondary">
                Voltage: {sensorData?.battery_voltage?.toFixed(1) || '0.0'}V
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Temperature Card */}
        <Grid item xs={12} md={6} lg={4}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TempIcon sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">Temperature</Typography>
              </Box>
              
              <Typography variant="h4" sx={{ fontWeight: 600, mb: 1 }}>
                {sensorData?.temperature?.toFixed(1) || '0.0'}°C
              </Typography>
              
              <Typography variant="body2" color="text.secondary">
                System temperature
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        {/* Sensor Data Card */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Sensor Readings
              </Typography>
              
              {sensorData ? (
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Accelerometer (m/s²)
                    </Typography>
                    <Typography variant="body1">
                      X: {sensorData.accelerometer[0]?.toFixed(2) || '0.00'}
                    </Typography>
                    <Typography variant="body1">
                      Y: {sensorData.accelerometer[1]?.toFixed(2) || '0.00'}
                    </Typography>
                    <Typography variant="body1">
                      Z: {sensorData.accelerometer[2]?.toFixed(2) || '0.00'}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Gyroscope (rad/s)
                    </Typography>
                    <Typography variant="body1">
                      X: {sensorData.gyroscope[0]?.toFixed(2) || '0.00'}
                    </Typography>
                    <Typography variant="body1">
                      Y: {sensorData.gyroscope[1]?.toFixed(2) || '0.00'}
                    </Typography>
                    <Typography variant="body1">
                      Z: {sensorData.gyroscope[2]?.toFixed(2) || '0.00'}
                    </Typography>
                  </Grid>
                  
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Distance Sensor
                    </Typography>
                    <Typography variant="body1">
                      {sensorData.ultrasonic_distance?.toFixed(2) || '0.00'} meters
                    </Typography>
                  </Grid>
                </Grid>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No sensor data available
                </Typography>
              )}
            </CardContent>
          </Card>
        </Grid>
        
        {/* Quick Actions Card */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '100%' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              
              <Grid container spacing={1}>
                <Grid item xs={12}>
                  <Button
                    fullWidth
                    variant="contained"
                    startIcon={<SpeakIcon />}
                    onClick={() => handleQuickAction('greet')}
                    sx={{ mb: 1 }}
                  >
                    Greet & Wave
                  </Button>
                </Grid>
                
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    startIcon={<PlayIcon />}
                    onClick={() => handleQuickAction('demo')}
                  >
                    Demo Dance
                  </Button>
                </Grid>
                
                <Grid item xs={6}>
                  <Button
                    fullWidth
                    variant="outlined"
                    color="error"
                    startIcon={<StopIcon />}
                    onClick={() => handleQuickAction('stop')}
                  >
                    Emergency Stop
                  </Button>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
        
        {/* System Monitoring Chart */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Monitoring
              </Typography>
              
              <Box sx={{ height: 300, mt: 2 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={systemStats}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="time" />
                    <YAxis />
                    <Tooltip />
                    <Line 
                      type="monotone" 
                      dataKey="battery" 
                      stroke="#4caf50" 
                      name="Battery %" 
                    />
                    <Line 
                      type="monotone" 
                      dataKey="temperature" 
                      stroke="#ff9800" 
                      name="Temperature °C" 
                    />
                    <Line 
                      type="monotone" 
                      dataKey="distance" 
                      stroke="#2196f3" 
                      name="Distance (m)" 
                    />
                  </LineChart>
                </ResponsiveContainer>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
}

export default Dashboard;
