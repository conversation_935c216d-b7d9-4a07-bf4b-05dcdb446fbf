"""
WebSocket Manager - Real-time communication with clients
Handles WebSocket connections for live robot control and monitoring
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Set
from fastapi import WebSocket, WebSocketDisconnect
from dataclasses import dataclass, asdict
import time

logger = logging.getLogger(__name__)

@dataclass
class ClientInfo:
    """Information about connected client"""
    websocket: WebSocket
    client_id: str
    connected_at: float
    last_ping: float
    client_type: str = "unknown"  # web, mobile, blockly, etc.

class WebSocketManager:
    """Manages WebSocket connections and real-time communication"""
    
    def __init__(self):
        # Active connections
        self.active_connections: Dict[str, ClientInfo] = {}
        self.connection_groups: Dict[str, Set[str]] = {
            "all": set(),
            "web": set(),
            "mobile": set(),
            "blockly": set(),
            "monitoring": set()
        }
        
        # Message handlers
        self.message_handlers = {
            "ping": self._handle_ping,
            "subscribe": self._handle_subscribe,
            "unsubscribe": self._handle_unsubscribe,
            "robot_command": self._handle_robot_command,
            "get_status": self._handle_get_status,
            "blockly_execute": self._handle_blockly_execute,
            "voice_command": self._handle_voice_command
        }
        
        # Robot controller reference (will be set externally)
        self.robot_controller = None
        
        # Start background tasks
        asyncio.create_task(self._connection_monitor())
        
        logger.info("WebSocket manager initialized")
    
    def set_robot_controller(self, controller):
        """Set robot controller reference"""
        self.robot_controller = controller
    
    async def connect(self, websocket: WebSocket, client_id: str = None):
        """Accept new WebSocket connection"""
        await websocket.accept()
        
        # Generate client ID if not provided
        if not client_id:
            client_id = f"client_{int(time.time() * 1000)}"
        
        # Create client info
        client_info = ClientInfo(
            websocket=websocket,
            client_id=client_id,
            connected_at=time.time(),
            last_ping=time.time()
        )
        
        # Store connection
        self.active_connections[client_id] = client_info
        self.connection_groups["all"].add(client_id)
        
        # Send welcome message
        await self._send_to_client(client_id, {
            "type": "connection_established",
            "client_id": client_id,
            "timestamp": time.time()
        })
        
        logger.info(f"Client {client_id} connected")
    
    def disconnect(self, websocket: WebSocket):
        """Handle client disconnection"""
        # Find and remove client
        client_id = None
        for cid, client_info in self.active_connections.items():
            if client_info.websocket == websocket:
                client_id = cid
                break
        
        if client_id:
            # Remove from all groups
            for group in self.connection_groups.values():
                group.discard(client_id)
            
            # Remove from active connections
            del self.active_connections[client_id]
            
            logger.info(f"Client {client_id} disconnected")
    
    async def handle_message(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle incoming WebSocket message"""
        try:
            message_type = data.get("type")
            
            if message_type in self.message_handlers:
                await self.message_handlers[message_type](websocket, data)
            else:
                await self._send_error(websocket, f"Unknown message type: {message_type}")
                
        except Exception as e:
            logger.error(f"Error handling WebSocket message: {e}")
            await self._send_error(websocket, f"Error processing message: {str(e)}")
    
    async def _handle_ping(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle ping message"""
        client_id = self._get_client_id(websocket)
        if client_id:
            self.active_connections[client_id].last_ping = time.time()
            await self._send_to_websocket(websocket, {
                "type": "pong",
                "timestamp": time.time()
            })
    
    async def _handle_subscribe(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle subscription to message groups"""
        client_id = self._get_client_id(websocket)
        groups = data.get("groups", [])
        
        if client_id:
            for group in groups:
                if group in self.connection_groups:
                    self.connection_groups[group].add(client_id)
                    
                    # Set client type based on subscription
                    if group in ["web", "mobile", "blockly"]:
                        self.active_connections[client_id].client_type = group
            
            await self._send_to_websocket(websocket, {
                "type": "subscribed",
                "groups": groups
            })
    
    async def _handle_unsubscribe(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle unsubscription from message groups"""
        client_id = self._get_client_id(websocket)
        groups = data.get("groups", [])
        
        if client_id:
            for group in groups:
                if group in self.connection_groups:
                    self.connection_groups[group].discard(client_id)
            
            await self._send_to_websocket(websocket, {
                "type": "unsubscribed",
                "groups": groups
            })
    
    async def _handle_robot_command(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle robot control commands"""
        if not self.robot_controller:
            await self._send_error(websocket, "Robot controller not available")
            return
        
        try:
            command = data.get("command", {})
            await self.robot_controller.add_command(command)
            
            await self._send_to_websocket(websocket, {
                "type": "command_accepted",
                "command_id": data.get("command_id"),
                "timestamp": time.time()
            })
            
            # Broadcast command to monitoring clients
            await self.broadcast_to_group("monitoring", {
                "type": "command_executed",
                "command": command,
                "timestamp": time.time()
            })
            
        except Exception as e:
            await self._send_error(websocket, f"Error executing command: {str(e)}")
    
    async def _handle_get_status(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle status request"""
        if not self.robot_controller:
            await self._send_error(websocket, "Robot controller not available")
            return
        
        try:
            status = self.robot_controller.get_status()
            hardware_status = self.robot_controller.hardware.get_hardware_status()
            ai_status = self.robot_controller.ai.get_ai_status()
            
            await self._send_to_websocket(websocket, {
                "type": "status_response",
                "data": {
                    "robot": status,
                    "hardware": hardware_status,
                    "ai": ai_status
                },
                "timestamp": time.time()
            })
            
        except Exception as e:
            await self._send_error(websocket, f"Error getting status: {str(e)}")
    
    async def _handle_blockly_execute(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle Blockly program execution"""
        if not self.robot_controller:
            await self._send_error(websocket, "Robot controller not available")
            return
        
        try:
            code = data.get("code", "")
            program_name = data.get("name", "Untitled")
            
            await self.robot_controller.add_command({
                "type": "program",
                "params": {
                    "code": code,
                    "name": program_name
                }
            })
            
            await self._send_to_websocket(websocket, {
                "type": "program_started",
                "program_name": program_name,
                "timestamp": time.time()
            })
            
        except Exception as e:
            await self._send_error(websocket, f"Error executing Blockly program: {str(e)}")
    
    async def _handle_voice_command(self, websocket: WebSocket, data: Dict[str, Any]):
        """Handle voice command processing"""
        if not self.robot_controller:
            await self._send_error(websocket, "Robot controller not available")
            return
        
        try:
            # Process voice command
            command_text = data.get("text", "").lower()
            
            # Simple voice command parsing
            if "move forward" in command_text:
                await self.robot_controller.add_command({
                    "type": "move",
                    "params": {"linear": 0.3, "angular": 0.0, "duration": 2.0}
                })
            elif "move backward" in command_text:
                await self.robot_controller.add_command({
                    "type": "move",
                    "params": {"linear": -0.3, "angular": 0.0, "duration": 2.0}
                })
            elif "turn left" in command_text:
                await self.robot_controller.add_command({
                    "type": "move",
                    "params": {"linear": 0.0, "angular": 0.5, "duration": 2.0}
                })
            elif "turn right" in command_text:
                await self.robot_controller.add_command({
                    "type": "move",
                    "params": {"linear": 0.0, "angular": -0.5, "duration": 2.0}
                })
            elif "wave" in command_text:
                await self.robot_controller.add_command({
                    "type": "dance",
                    "params": {"dance": "wave"}
                })
            elif "hello" in command_text or "hi" in command_text:
                await self.robot_controller.add_command({
                    "type": "speak",
                    "params": {"text": "Hello! Nice to meet you!"}
                })
            elif "stop" in command_text:
                await self.robot_controller.hardware.stop_robot()
            else:
                # Echo back unrecognized command
                await self.robot_controller.add_command({
                    "type": "speak",
                    "params": {"text": f"I heard you say: {command_text}"}
                })
            
            await self._send_to_websocket(websocket, {
                "type": "voice_command_processed",
                "command": command_text,
                "timestamp": time.time()
            })
            
        except Exception as e:
            await self._send_error(websocket, f"Error processing voice command: {str(e)}")
    
    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast message to all connected clients"""
        await self.broadcast_to_group("all", message)
    
    async def broadcast_to_group(self, group: str, message: Dict[str, Any]):
        """Broadcast message to specific group"""
        if group not in self.connection_groups:
            return
        
        disconnected_clients = []
        
        for client_id in self.connection_groups[group].copy():
            if client_id in self.active_connections:
                try:
                    await self._send_to_client(client_id, message)
                except Exception as e:
                    logger.error(f"Error sending to client {client_id}: {e}")
                    disconnected_clients.append(client_id)
        
        # Remove disconnected clients
        for client_id in disconnected_clients:
            self._remove_client(client_id)
    
    async def _send_to_client(self, client_id: str, message: Dict[str, Any]):
        """Send message to specific client"""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id].websocket
            await self._send_to_websocket(websocket, message)
    
    async def _send_to_websocket(self, websocket: WebSocket, message: Dict[str, Any]):
        """Send message to WebSocket"""
        try:
            await websocket.send_json(message)
        except Exception as e:
            logger.error(f"Error sending WebSocket message: {e}")
            raise
    
    async def _send_error(self, websocket: WebSocket, error_message: str):
        """Send error message to client"""
        await self._send_to_websocket(websocket, {
            "type": "error",
            "message": error_message,
            "timestamp": time.time()
        })
    
    def _get_client_id(self, websocket: WebSocket) -> str:
        """Get client ID from WebSocket"""
        for client_id, client_info in self.active_connections.items():
            if client_info.websocket == websocket:
                return client_id
        return None
    
    def _remove_client(self, client_id: str):
        """Remove client from all groups and connections"""
        if client_id in self.active_connections:
            # Remove from all groups
            for group in self.connection_groups.values():
                group.discard(client_id)
            
            # Remove from active connections
            del self.active_connections[client_id]
    
    async def _connection_monitor(self):
        """Monitor connections and remove stale ones"""
        while True:
            try:
                current_time = time.time()
                stale_clients = []
                
                for client_id, client_info in self.active_connections.items():
                    # Check for stale connections (no ping for 60 seconds)
                    if current_time - client_info.last_ping > 60:
                        stale_clients.append(client_id)
                
                # Remove stale clients
                for client_id in stale_clients:
                    logger.info(f"Removing stale client: {client_id}")
                    self._remove_client(client_id)
                
                # Wait before next check
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in connection monitor: {e}")
                await asyncio.sleep(30)
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        return {
            "total_connections": len(self.active_connections),
            "groups": {
                group: len(clients) 
                for group, clients in self.connection_groups.items()
            },
            "clients": [
                {
                    "client_id": client_id,
                    "client_type": client_info.client_type,
                    "connected_at": client_info.connected_at,
                    "last_ping": client_info.last_ping
                }
                for client_id, client_info in self.active_connections.items()
            ]
        }
