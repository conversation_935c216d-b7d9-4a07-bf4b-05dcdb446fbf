/**
 * Robot API Service - HTTP client for robot control
 * Provides methods for all robot API endpoints
 */

import axios from 'axios';

export class RobotAPI {
  constructor(baseURL = '/api/v1') {
    this.baseURL = baseURL;
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });
    
    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response.data,
      (error) => {
        console.error('API Error:', error);
        throw new Error(error.response?.data?.detail || error.message);
      }
    );
  }
  
  // Status and Information
  async getStatus() {
    return await this.client.get('/status');
  }
  
  async getSensorData() {
    return await this.client.get('/sensors');
  }
  
  async getConfiguration() {
    return await this.client.get('/config');
  }
  
  // Movement Control
  async moveRobot(linear, angular, duration = 0) {
    return await this.client.post('/move', {
      linear,
      angular,
      duration
    });
  }
  
  async stopRobot() {
    return await this.client.post('/stop');
  }
  
  // Servo Control
  async controlServo(servo, angle, speed = 1.0) {
    return await this.client.post('/servo', {
      servo,
      angle,
      speed
    });
  }
  
  async getServoAngle(servoName) {
    return await this.client.get(`/servo/${servoName}`);
  }
  
  async listServos() {
    return await this.client.get('/servos');
  }
  
  // Voice and Speech
  async speakText(text, language = 'en') {
    return await this.client.post('/speak', {
      text,
      language
    });
  }
  
  async startVoiceRecognition() {
    return await this.client.post('/voice/start');
  }
  
  async stopVoiceRecognition() {
    return await this.client.post('/voice/stop');
  }
  
  // Dance and Entertainment
  async performDance(dance) {
    return await this.client.post('/dance', {
      dance
    });
  }
  
  async listDances() {
    return await this.client.get('/dances');
  }
  
  // Programming
  async executeProgram(code, name = null) {
    return await this.client.post('/program/execute', {
      code,
      name
    });
  }
  
  // Face Recognition
  async startFaceRecognition() {
    return await this.client.post('/face/start');
  }
  
  async stopFaceRecognition() {
    return await this.client.post('/face/stop');
  }
  
  async toggleFaceTracking(enabled) {
    return await this.client.post('/face/track', null, {
      params: { enabled }
    });
  }
  
  // Autonomous Mode
  async toggleAutonomousMode(enabled) {
    return await this.client.post('/autonomous', null, {
      params: { enabled }
    });
  }
  
  // Power Management
  async enterSleepMode() {
    return await this.client.post('/sleep');
  }
  
  async wakeUpRobot() {
    return await this.client.post('/wake');
  }
  
  // Configuration
  async updateConfiguration(section, settings) {
    return await this.client.post('/config', {
      section,
      settings
    });
  }
  
  // Convenience methods for common operations
  async quickMove(direction, duration = 2.0) {
    const movements = {
      forward: { linear: 0.3, angular: 0.0 },
      backward: { linear: -0.3, angular: 0.0 },
      left: { linear: 0.0, angular: 0.5 },
      right: { linear: 0.0, angular: -0.5 },
      stop: { linear: 0.0, angular: 0.0 }
    };
    
    const movement = movements[direction];
    if (!movement) {
      throw new Error(`Unknown direction: ${direction}`);
    }
    
    return await this.moveRobot(movement.linear, movement.angular, duration);
  }
  
  async setServoPosition(positions) {
    const promises = Object.entries(positions).map(([servo, angle]) =>
      this.controlServo(servo, angle)
    );
    
    return await Promise.all(promises);
  }
  
  async performGesture(gesture) {
    const gestures = {
      wave: async () => {
        await this.controlServo('right_shoulder', 45);
        await new Promise(resolve => setTimeout(resolve, 500));
        await this.controlServo('right_elbow', 45);
        await new Promise(resolve => setTimeout(resolve, 500));
        await this.controlServo('right_shoulder', 135);
        await new Promise(resolve => setTimeout(resolve, 500));
        await this.controlServo('right_elbow', 135);
        await new Promise(resolve => setTimeout(resolve, 500));
        await this.controlServo('right_shoulder', 90);
        await this.controlServo('right_elbow', 90);
      },
      
      nod: async () => {
        await this.controlServo('neck', 60);
        await new Promise(resolve => setTimeout(resolve, 500));
        await this.controlServo('neck', 120);
        await new Promise(resolve => setTimeout(resolve, 500));
        await this.controlServo('neck', 90);
      },
      
      celebrate: async () => {
        await this.setServoPosition({
          left_shoulder: 45,
          right_shoulder: 45,
          left_elbow: 45,
          right_elbow: 45
        });
        await new Promise(resolve => setTimeout(resolve, 300));
        await this.setServoPosition({
          left_shoulder: 135,
          right_shoulder: 135
        });
        await new Promise(resolve => setTimeout(resolve, 300));
        await this.setServoPosition({
          left_shoulder: 90,
          right_shoulder: 90,
          left_elbow: 90,
          right_elbow: 90
        });
      },
      
      reset: async () => {
        await this.setServoPosition({
          neck: 90,
          left_shoulder: 90,
          left_elbow: 90,
          right_shoulder: 90,
          right_elbow: 90
        });
      }
    };
    
    const gestureFunction = gestures[gesture];
    if (!gestureFunction) {
      throw new Error(`Unknown gesture: ${gesture}`);
    }
    
    return await gestureFunction();
  }
  
  async sayAndDo(text, action = null) {
    // Speak text and optionally perform an action
    const speakPromise = this.speakText(text);
    
    if (action) {
      // Start action after a short delay
      setTimeout(async () => {
        try {
          if (typeof action === 'string') {
            await this.performGesture(action);
          } else if (typeof action === 'function') {
            await action();
          }
        } catch (error) {
          console.error('Error performing action:', error);
        }
      }, 500);
    }
    
    return await speakPromise;
  }
  
  // Health check
  async healthCheck() {
    try {
      const response = await axios.get('/health', { timeout: 5000 });
      return response.data;
    } catch (error) {
      throw new Error('Robot is not responding');
    }
  }
  
  // Batch operations
  async batchCommands(commands) {
    const results = [];
    
    for (const command of commands) {
      try {
        let result;
        
        switch (command.type) {
          case 'move':
            result = await this.moveRobot(
              command.params.linear,
              command.params.angular,
              command.params.duration
            );
            break;
            
          case 'servo':
            result = await this.controlServo(
              command.params.servo,
              command.params.angle,
              command.params.speed
            );
            break;
            
          case 'speak':
            result = await this.speakText(
              command.params.text,
              command.params.language
            );
            break;
            
          case 'dance':
            result = await this.performDance(command.params.dance);
            break;
            
          case 'delay':
            await new Promise(resolve => 
              setTimeout(resolve, command.params.duration * 1000)
            );
            result = { success: true, message: 'Delay completed' };
            break;
            
          default:
            throw new Error(`Unknown command type: ${command.type}`);
        }
        
        results.push({ success: true, result });
        
        // Add delay between commands if specified
        if (command.delay) {
          await new Promise(resolve => 
            setTimeout(resolve, command.delay * 1000)
          );
        }
        
      } catch (error) {
        results.push({ 
          success: false, 
          error: error.message,
          command: command.type 
        });
        
        // Stop batch execution on error if specified
        if (command.stopOnError) {
          break;
        }
      }
    }
    
    return results;
  }
}
