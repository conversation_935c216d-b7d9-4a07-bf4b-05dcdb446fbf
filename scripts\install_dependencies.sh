#!/bin/bash

# STEM_XPERT Robot Dependencies Installation Script
# This script installs all required system and Python dependencies

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

log "Installing STEM_XPERT Robot dependencies..."

# Detect OS
if [[ "$OSTYPE" == "linux-gnu"* ]]; then
    if [ -f /etc/os-release ]; then
        . /etc/os-release
        OS=$NAME
        VER=$VERSION_ID
    fi
elif [[ "$OSTYPE" == "darwin"* ]]; then
    OS="macOS"
elif [[ "$OSTYPE" == "msys" ]]; then
    OS="Windows"
else
    OS="Unknown"
fi

log "Detected OS: $OS"

# Install system dependencies based on OS
install_system_deps() {
    case $OS in
        *"Ubuntu"*|*"Debian"*)
            log "Installing system dependencies for Ubuntu/Debian..."
            sudo apt-get update
            sudo apt-get install -y \
                python3 \
                python3-pip \
                python3-venv \
                python3-dev \
                build-essential \
                cmake \
                pkg-config \
                libjpeg-dev \
                libtiff5-dev \
                libpng-dev \
                libavcodec-dev \
                libavformat-dev \
                libswscale-dev \
                libv4l-dev \
                libxvidcore-dev \
                libx264-dev \
                libgtk-3-dev \
                libatlas-base-dev \
                gfortran \
                libportaudio2 \
                libportaudiocpp0 \
                portaudio19-dev \
                libasound2-dev \
                libpulse-dev \
                espeak \
                espeak-data \
                libespeak1 \
                libespeak-dev \
                ffmpeg \
                nodejs \
                npm
            
            # Raspberry Pi specific packages
            if grep -q "Raspberry Pi" /proc/cpuinfo 2>/dev/null; then
                log "Installing Raspberry Pi specific packages..."
                sudo apt-get install -y \
                    python3-rpi.gpio \
                    i2c-tools \
                    python3-smbus \
                    raspi-gpio
                
                # Enable I2C and camera
                sudo raspi-config nonint do_i2c 0
                sudo raspi-config nonint do_camera 0
            fi
            ;;
            
        *"CentOS"*|*"Red Hat"*|*"Fedora"*)
            log "Installing system dependencies for CentOS/RHEL/Fedora..."
            sudo yum install -y \
                python3 \
                python3-pip \
                python3-devel \
                gcc \
                gcc-c++ \
                cmake \
                pkgconfig \
                libjpeg-turbo-devel \
                libpng-devel \
                libtiff-devel \
                opencv-devel \
                portaudio-devel \
                alsa-lib-devel \
                pulseaudio-libs-devel \
                espeak \
                ffmpeg \
                nodejs \
                npm
            ;;
            
        "macOS")
            log "Installing system dependencies for macOS..."
            if ! command -v brew &> /dev/null; then
                error "Homebrew not found. Please install Homebrew first."
                exit 1
            fi
            
            brew install \
                python3 \
                cmake \
                pkg-config \
                jpeg \
                libpng \
                libtiff \
                opencv \
                portaudio \
                espeak \
                ffmpeg \
                node
            ;;
            
        *)
            warning "Unsupported OS: $OS. You may need to install dependencies manually."
            ;;
    esac
}

# Install system dependencies
install_system_deps

# Create virtual environment
log "Creating Python virtual environment..."
cd "$PROJECT_DIR"
python3 -m venv venv
source venv/bin/activate

# Upgrade pip
log "Upgrading pip..."
pip install --upgrade pip setuptools wheel

# Install Python dependencies
log "Installing Python dependencies..."
pip install -r requirements.txt

# Install additional dependencies for Raspberry Pi
if grep -q "Raspberry Pi" /proc/cpuinfo 2>/dev/null; then
    log "Installing Raspberry Pi specific Python packages..."
    pip install \
        RPi.GPIO \
        adafruit-circuitpython-servokit \
        adafruit-circuitpython-motor \
        adafruit-circuitpython-mpu6050
fi

# Install Node.js dependencies for frontend
if [ -f "$PROJECT_DIR/frontend/package.json" ]; then
    log "Installing Node.js dependencies..."
    cd "$PROJECT_DIR/frontend"
    npm install
    
    # Build frontend for production
    log "Building frontend for production..."
    npm run build
    
    success "Frontend built successfully"
fi

# Create necessary directories
log "Creating necessary directories..."
mkdir -p "$PROJECT_DIR/logs"
mkdir -p "$PROJECT_DIR/models"
mkdir -p "$PROJECT_DIR/data"
mkdir -p "$PROJECT_DIR/config"

# Set up permissions
log "Setting up permissions..."
chmod +x "$PROJECT_DIR/scripts/"*.sh

# Download pre-trained models (if needed)
log "Setting up AI models..."
cd "$PROJECT_DIR"

# Create models directory structure
mkdir -p models/face_recognition
mkdir -p models/gesture_recognition
mkdir -p models/object_detection

# Download face recognition models (placeholder)
# In a real implementation, you would download actual model files
echo "# Face recognition models go here" > models/face_recognition/README.md
echo "# Gesture recognition models go here" > models/gesture_recognition/README.md
echo "# Object detection models go here" > models/object_detection/README.md

# Test installation
log "Testing installation..."
source venv/bin/activate

# Test Python imports
python3 -c "
import sys
try:
    import fastapi
    import uvicorn
    import cv2
    import numpy
    import pydantic
    print('✓ Core dependencies imported successfully')
except ImportError as e:
    print(f'✗ Import error: {e}')
    sys.exit(1)
"

# Test hardware-specific imports (if on Raspberry Pi)
if grep -q "Raspberry Pi" /proc/cpuinfo 2>/dev/null; then
    python3 -c "
try:
    import RPi.GPIO
    import board
    import busio
    print('✓ Raspberry Pi hardware libraries imported successfully')
except ImportError as e:
    print(f'⚠ Hardware library warning: {e}')
"
fi

success "Installation completed successfully!"

# Display summary
echo ""
echo "🤖 STEM_XPERT Robot Installation Summary"
echo "========================================"
echo ""
echo "✓ System dependencies installed"
echo "✓ Python virtual environment created"
echo "✓ Python dependencies installed"
echo "✓ Frontend dependencies installed"
echo "✓ Frontend built for production"
echo "✓ Directory structure created"
echo "✓ Permissions set"
echo ""
echo "📁 Project structure:"
echo "   $PROJECT_DIR/"
echo "   ├── backend/          # Python backend code"
echo "   ├── frontend/         # React frontend code"
echo "   ├── config/           # Configuration files"
echo "   ├── scripts/          # Utility scripts"
echo "   ├── models/           # AI model files"
echo "   ├── logs/             # Log files"
echo "   └── venv/             # Python virtual environment"
echo ""
echo "🚀 Next steps:"
echo "   1. Review configuration: config/robot_config.yaml"
echo "   2. Start the robot: ./scripts/start_robot.sh"
echo "   3. Open web interface: http://localhost:8000"
echo ""
echo "📖 For more information, see README.md"
echo ""
