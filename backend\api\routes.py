"""
API Routes - REST endpoints for robot control
Provides HTTP API for all robot functionality
"""

import asyncio
import logging
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from pydantic import BaseModel, Field

# Import robot controller (will be injected)
from core.robot_controller import RobotController

logger = logging.getLogger(__name__)

# Create router
router = APIRouter()

# Pydantic models for request/response
class MoveCommand(BaseModel):
    linear: float = Field(..., ge=-1.0, le=1.0, description="Linear velocity (-1 to 1)")
    angular: float = Field(..., ge=-2.0, le=2.0, description="Angular velocity (-2 to 2)")
    duration: float = Field(0.0, ge=0.0, le=10.0, description="Duration in seconds")

class ServoCommand(BaseModel):
    servo: str = Field(..., description="Servo name")
    angle: float = Field(..., ge=0, le=180, description="Target angle")
    speed: float = Field(1.0, ge=0.1, le=5.0, description="Movement speed")

class SpeakCommand(BaseModel):
    text: str = Field(..., max_length=500, description="Text to speak")
    language: str = Field("en", description="Language code")

class DanceCommand(BaseModel):
    dance: str = Field(..., description="Dance sequence name")

class ProgramCommand(BaseModel):
    code: str = Field(..., description="Blockly-generated code")
    name: Optional[str] = Field(None, description="Program name")

class ConfigUpdate(BaseModel):
    section: str = Field(..., description="Config section")
    settings: Dict[str, Any] = Field(..., description="Settings to update")

class RobotResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None

# Global robot controller reference (injected at startup)
robot_controller: Optional[RobotController] = None

def set_robot_controller(controller: RobotController):
    """Set robot controller reference"""
    global robot_controller
    robot_controller = controller

# Status and Information Endpoints
@router.get("/status", response_model=RobotResponse)
async def get_robot_status():
    """Get current robot status"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        status = robot_controller.get_status()
        hardware_status = robot_controller.hardware.get_hardware_status()
        ai_status = robot_controller.ai.get_ai_status()
        
        return RobotResponse(
            success=True,
            message="Status retrieved successfully",
            data={
                "robot": status,
                "hardware": hardware_status,
                "ai": ai_status
            }
        )
    except Exception as e:
        logger.error(f"Error getting status: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/sensors", response_model=RobotResponse)
async def get_sensor_data():
    """Get current sensor readings"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        sensor_data = robot_controller.hardware.get_sensor_data()
        
        return RobotResponse(
            success=True,
            message="Sensor data retrieved successfully",
            data={
                "accelerometer": sensor_data.accelerometer,
                "gyroscope": sensor_data.gyroscope,
                "temperature": sensor_data.temperature,
                "ultrasonic_distance": sensor_data.ultrasonic_distance,
                "battery_voltage": sensor_data.battery_voltage
            }
        )
    except Exception as e:
        logger.error(f"Error getting sensor data: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Movement Control Endpoints
@router.post("/move", response_model=RobotResponse)
async def move_robot(command: MoveCommand):
    """Move robot with specified linear and angular velocities"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        await robot_controller.add_command({
            "type": "move",
            "params": command.dict()
        })
        
        return RobotResponse(
            success=True,
            message=f"Move command executed: linear={command.linear}, angular={command.angular}"
        )
    except Exception as e:
        logger.error(f"Error moving robot: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/stop", response_model=RobotResponse)
async def stop_robot():
    """Emergency stop robot movement"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        await robot_controller.hardware.stop_robot()
        
        return RobotResponse(
            success=True,
            message="Robot stopped successfully"
        )
    except Exception as e:
        logger.error(f"Error stopping robot: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Servo Control Endpoints
@router.post("/servo", response_model=RobotResponse)
async def control_servo(command: ServoCommand):
    """Control individual servo motor"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        await robot_controller.add_command({
            "type": "servo",
            "params": command.dict()
        })
        
        return RobotResponse(
            success=True,
            message=f"Servo {command.servo} moved to {command.angle} degrees"
        )
    except Exception as e:
        logger.error(f"Error controlling servo: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/servo/{servo_name}", response_model=RobotResponse)
async def get_servo_angle(servo_name: str):
    """Get current servo angle"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        angle = await robot_controller.hardware.get_servo_angle(servo_name)
        
        if angle is None:
            raise HTTPException(status_code=404, detail=f"Servo {servo_name} not found")
        
        return RobotResponse(
            success=True,
            message=f"Servo {servo_name} angle retrieved",
            data={"angle": angle}
        )
    except Exception as e:
        logger.error(f"Error getting servo angle: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/servos", response_model=RobotResponse)
async def list_servos():
    """List all available servos"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        servos = list(robot_controller.config.hardware.servos.keys())
        
        return RobotResponse(
            success=True,
            message="Servos listed successfully",
            data={"servos": servos}
        )
    except Exception as e:
        logger.error(f"Error listing servos: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Voice and Speech Endpoints
@router.post("/speak", response_model=RobotResponse)
async def speak_text(command: SpeakCommand):
    """Make robot speak text"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        await robot_controller.add_command({
            "type": "speak",
            "params": command.dict()
        })
        
        return RobotResponse(
            success=True,
            message=f"Speaking: {command.text}"
        )
    except Exception as e:
        logger.error(f"Error speaking text: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/voice/start", response_model=RobotResponse)
async def start_voice_recognition():
    """Start voice recognition"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        await robot_controller.ai.start_voice_recognition()
        
        return RobotResponse(
            success=True,
            message="Voice recognition started"
        )
    except Exception as e:
        logger.error(f"Error starting voice recognition: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/voice/stop", response_model=RobotResponse)
async def stop_voice_recognition():
    """Stop voice recognition"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        await robot_controller.ai.stop_voice_recognition()
        
        return RobotResponse(
            success=True,
            message="Voice recognition stopped"
        )
    except Exception as e:
        logger.error(f"Error stopping voice recognition: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Dance and Entertainment Endpoints
@router.post("/dance", response_model=RobotResponse)
async def perform_dance(command: DanceCommand):
    """Perform dance sequence"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        await robot_controller.add_command({
            "type": "dance",
            "params": command.dict()
        })
        
        return RobotResponse(
            success=True,
            message=f"Performing dance: {command.dance}"
        )
    except Exception as e:
        logger.error(f"Error performing dance: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/dances", response_model=RobotResponse)
async def list_dances():
    """List available dance sequences"""
    try:
        dances = ["wave", "nod", "celebrate", "default"]
        
        return RobotResponse(
            success=True,
            message="Dances listed successfully",
            data={"dances": dances}
        )
    except Exception as e:
        logger.error(f"Error listing dances: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Programming Endpoints
@router.post("/program/execute", response_model=RobotResponse)
async def execute_program(command: ProgramCommand):
    """Execute Blockly program"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        await robot_controller.add_command({
            "type": "program",
            "params": command.dict()
        })
        
        return RobotResponse(
            success=True,
            message="Program execution started"
        )
    except Exception as e:
        logger.error(f"Error executing program: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Face Recognition Endpoints
@router.post("/face/start", response_model=RobotResponse)
async def start_face_recognition():
    """Start face recognition"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        await robot_controller.ai.start_face_recognition()
        
        return RobotResponse(
            success=True,
            message="Face recognition started"
        )
    except Exception as e:
        logger.error(f"Error starting face recognition: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/face/stop", response_model=RobotResponse)
async def stop_face_recognition():
    """Stop face recognition"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        await robot_controller.ai.stop_face_recognition()
        
        return RobotResponse(
            success=True,
            message="Face recognition stopped"
        )
    except Exception as e:
        logger.error(f"Error stopping face recognition: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/face/track", response_model=RobotResponse)
async def toggle_face_tracking(enabled: bool = True):
    """Enable/disable face tracking"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        await robot_controller.add_command({
            "type": "face_track",
            "params": {"enabled": enabled}
        })
        
        return RobotResponse(
            success=True,
            message=f"Face tracking {'enabled' if enabled else 'disabled'}"
        )
    except Exception as e:
        logger.error(f"Error toggling face tracking: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Autonomous Mode Endpoints
@router.post("/autonomous", response_model=RobotResponse)
async def toggle_autonomous_mode(enabled: bool = True):
    """Enable/disable autonomous mode"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        await robot_controller.add_command({
            "type": "autonomous",
            "params": {"enabled": enabled}
        })
        
        return RobotResponse(
            success=True,
            message=f"Autonomous mode {'enabled' if enabled else 'disabled'}"
        )
    except Exception as e:
        logger.error(f"Error toggling autonomous mode: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Power Management Endpoints
@router.post("/sleep", response_model=RobotResponse)
async def enter_sleep_mode():
    """Put robot to sleep"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        await robot_controller._enter_sleep_mode()
        
        return RobotResponse(
            success=True,
            message="Robot entering sleep mode"
        )
    except Exception as e:
        logger.error(f"Error entering sleep mode: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/wake", response_model=RobotResponse)
async def wake_up_robot():
    """Wake up robot from sleep"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        await robot_controller.wake_up()
        
        return RobotResponse(
            success=True,
            message="Robot waking up"
        )
    except Exception as e:
        logger.error(f"Error waking up robot: {e}")
        raise HTTPException(status_code=500, detail=str(e))

# Configuration Endpoints
@router.get("/config", response_model=RobotResponse)
async def get_configuration():
    """Get robot configuration"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        config_data = {
            "hardware": robot_controller.config.hardware.dict(),
            "ai": robot_controller.config.ai.dict(),
            "network": robot_controller.config.network.dict(),
            "robot": robot_controller.config.robot.dict()
        }
        
        return RobotResponse(
            success=True,
            message="Configuration retrieved successfully",
            data=config_data
        )
    except Exception as e:
        logger.error(f"Error getting configuration: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/config", response_model=RobotResponse)
async def update_configuration(update: ConfigUpdate):
    """Update robot configuration"""
    try:
        if not robot_controller:
            raise HTTPException(status_code=503, detail="Robot controller not available")
        
        # Update configuration (simplified implementation)
        # In practice, you'd want more validation and restart handling
        
        return RobotResponse(
            success=True,
            message=f"Configuration section {update.section} updated"
        )
    except Exception as e:
        logger.error(f"Error updating configuration: {e}")
        raise HTTPException(status_code=500, detail=str(e))
