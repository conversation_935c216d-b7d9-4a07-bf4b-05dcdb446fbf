import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Drawer,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Typography,
  Box,
  Chip,
  Divider,
  Avatar
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  ControlCamera as ControlIcon,
  Code as CodeIcon,
  RecordVoiceOver as VoiceIcon,
  Face as FaceIcon,
  School as EducationIcon,
  Settings as SettingsIcon,
  SmartToy as RobotIcon,
  Battery90 as BatteryIcon,
  Wifi as WifiIcon,
  WifiOff as WifiOffIcon
} from '@mui/icons-material';

const drawerWidth = 280;

const navigationItems = [
  {
    text: 'Dashboard',
    icon: <DashboardIcon />,
    path: '/dashboard',
    description: 'Robot status and overview'
  },
  {
    text: 'Robot Control',
    icon: <ControlIcon />,
    path: '/control',
    description: 'Manual robot control'
  },
  {
    text: 'Programming',
    icon: <CodeIcon />,
    path: '/programming',
    description: 'Blockly visual programming'
  },
  {
    text: 'Voice Control',
    icon: <VoiceIcon />,
    path: '/voice',
    description: 'Voice commands and TTS'
  },
  {
    text: 'Face Recognition',
    icon: <FaceIcon />,
    path: '/face',
    description: 'Face detection and tracking'
  },
  {
    text: 'Education',
    icon: <EducationIcon />,
    path: '/education',
    description: 'Learning modules and tutorials'
  },
  {
    text: 'Settings',
    icon: <SettingsIcon />,
    path: '/settings',
    description: 'Robot configuration'
  }
];

function Navigation({ robotStatus }) {
  const navigate = useNavigate();
  const location = useLocation();
  
  const getBatteryColor = (level) => {
    if (level > 60) return 'success';
    if (level > 30) return 'warning';
    return 'error';
  };
  
  const getConnectionStatus = () => {
    if (robotStatus.connected) {
      return {
        icon: <WifiIcon />,
        text: 'Connected',
        color: 'success'
      };
    } else {
      return {
        icon: <WifiOffIcon />,
        text: 'Disconnected',
        color: 'error'
      };
    }
  };
  
  const connectionStatus = getConnectionStatus();
  
  return (
    <Drawer
      variant="permanent"
      sx={{
        width: drawerWidth,
        flexShrink: 0,
        '& .MuiDrawer-paper': {
          width: drawerWidth,
          boxSizing: 'border-box',
          background: 'linear-gradient(180deg, #2d2d2d 0%, #1a1a1a 100%)',
          borderRight: '1px solid rgba(255, 255, 255, 0.1)',
        },
      }}
    >
      {/* Header */}
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Avatar
          sx={{
            width: 60,
            height: 60,
            margin: '0 auto 16px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          }}
        >
          <RobotIcon sx={{ fontSize: 32 }} />
        </Avatar>
        
        <Typography variant="h6" sx={{ fontWeight: 600, mb: 1 }}>
          STEM_XPERT
        </Typography>
        
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          Educational Robot
        </Typography>
        
        {/* Connection Status */}
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
          <Chip
            icon={connectionStatus.icon}
            label={connectionStatus.text}
            color={connectionStatus.color}
            size="small"
            variant="outlined"
          />
        </Box>
        
        {/* Battery Status */}
        {robotStatus.connected && (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Chip
              icon={<BatteryIcon />}
              label={`${Math.round(robotStatus.battery || 0)}%`}
              color={getBatteryColor(robotStatus.battery || 0)}
              size="small"
              variant="outlined"
            />
          </Box>
        )}
      </Box>
      
      <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.1)' }} />
      
      {/* Navigation Items */}
      <List sx={{ px: 2, py: 1 }}>
        {navigationItems.map((item) => {
          const isActive = location.pathname === item.path;
          
          return (
            <ListItem key={item.text} disablePadding sx={{ mb: 0.5 }}>
              <ListItemButton
                onClick={() => navigate(item.path)}
                sx={{
                  borderRadius: 2,
                  py: 1.5,
                  px: 2,
                  backgroundColor: isActive ? 'rgba(102, 126, 234, 0.2)' : 'transparent',
                  border: isActive ? '1px solid rgba(102, 126, 234, 0.3)' : '1px solid transparent',
                  '&:hover': {
                    backgroundColor: isActive 
                      ? 'rgba(102, 126, 234, 0.3)' 
                      : 'rgba(255, 255, 255, 0.05)',
                  },
                  transition: 'all 0.2s ease-in-out',
                }}
              >
                <ListItemIcon
                  sx={{
                    color: isActive ? '#667eea' : 'text.secondary',
                    minWidth: 40,
                  }}
                >
                  {item.icon}
                </ListItemIcon>
                
                <ListItemText
                  primary={
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: isActive ? 600 : 400,
                        color: isActive ? '#667eea' : 'text.primary',
                      }}
                    >
                      {item.text}
                    </Typography>
                  }
                  secondary={
                    <Typography
                      variant="caption"
                      sx={{
                        color: 'text.secondary',
                        fontSize: '0.75rem',
                        lineHeight: 1.2,
                        mt: 0.5,
                        display: 'block',
                      }}
                    >
                      {item.description}
                    </Typography>
                  }
                />
              </ListItemButton>
            </ListItem>
          );
        })}
      </List>
      
      <Box sx={{ flexGrow: 1 }} />
      
      {/* Footer */}
      <Box sx={{ p: 2, textAlign: 'center' }}>
        <Divider sx={{ borderColor: 'rgba(255, 255, 255, 0.1)', mb: 2 }} />
        
        {robotStatus.connected && (
          <Box sx={{ mb: 2 }}>
            <Typography variant="caption" color="text.secondary" display="block">
              Robot State
            </Typography>
            <Chip
              label={robotStatus.state || 'Unknown'}
              size="small"
              color="primary"
              variant="outlined"
              sx={{ mt: 0.5 }}
            />
          </Box>
        )}
        
        <Typography variant="caption" color="text.secondary" display="block">
          Version 1.0.0
        </Typography>
        
        <Typography variant="caption" color="text.secondary" display="block">
          © 2024 STEM_XPERT
        </Typography>
      </Box>
    </Drawer>
  );
}

export default Navigation;
