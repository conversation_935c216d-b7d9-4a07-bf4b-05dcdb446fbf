#!/usr/bin/env python3
"""
Simple API test script to verify robot functionality
"""

import requests
import json
import time

def test_api():
    base_url = "http://localhost:8000/api/v1"
    
    print("🧪 Testing STEM_XPERT Robot API")
    print("=" * 40)
    
    # Test 1: Health check
    print("\n1. Testing health endpoint...")
    try:
        response = requests.get("http://localhost:8000/health")
        if response.status_code == 200:
            print("   ✓ Health check passed")
            print(f"   Response: {response.json()}")
        else:
            print(f"   ✗ Health check failed: {response.status_code}")
    except Exception as e:
        print(f"   ✗ Health check error: {e}")
        return False
    
    # Test 2: Robot status
    print("\n2. Testing robot status...")
    try:
        response = requests.get(f"{base_url}/status")
        if response.status_code == 200:
            print("   ✓ Status endpoint working")
            data = response.json()
            print(f"   Robot state: {data['data']['robot']['state']}")
            print(f"   Uptime: {data['data']['robot']['uptime']:.1f}s")
        else:
            print(f"   ✗ Status failed: {response.status_code}")
    except Exception as e:
        print(f"   ✗ Status error: {e}")
    
    # Test 3: List servos
    print("\n3. Testing servo list...")
    try:
        response = requests.get(f"{base_url}/servos")
        if response.status_code == 200:
            print("   ✓ Servo list working")
            data = response.json()
            print(f"   Available servos: {data['data']['servos']}")
        else:
            print(f"   ✗ Servo list failed: {response.status_code}")
    except Exception as e:
        print(f"   ✗ Servo list error: {e}")
    
    # Test 4: Move servo
    print("\n4. Testing servo control...")
    try:
        servo_data = {
            "servo": "neck",
            "angle": 45,
            "speed": 1.0
        }
        response = requests.post(f"{base_url}/servo", json=servo_data)
        if response.status_code == 200:
            print("   ✓ Servo control working")
            data = response.json()
            print(f"   Response: {data['message']}")
        else:
            print(f"   ✗ Servo control failed: {response.status_code}")
            print(f"   Error: {response.text}")
    except Exception as e:
        print(f"   ✗ Servo control error: {e}")
    
    # Test 5: Text-to-speech
    print("\n5. Testing text-to-speech...")
    try:
        tts_data = {
            "text": "Hello! I am STEM_XPERT robot running in simulation mode.",
            "language": "en"
        }
        response = requests.post(f"{base_url}/speak", json=tts_data)
        if response.status_code == 200:
            print("   ✓ Text-to-speech working")
            data = response.json()
            print(f"   Response: {data['message']}")
        else:
            print(f"   ✗ TTS failed: {response.status_code}")
    except Exception as e:
        print(f"   ✗ TTS error: {e}")
    
    # Test 6: Robot movement
    print("\n6. Testing robot movement...")
    try:
        move_data = {
            "linear": 0.2,
            "angular": 0.0,
            "duration": 2.0
        }
        response = requests.post(f"{base_url}/move", json=move_data)
        if response.status_code == 200:
            print("   ✓ Robot movement working")
            data = response.json()
            print(f"   Response: {data['message']}")
        else:
            print(f"   ✗ Movement failed: {response.status_code}")
    except Exception as e:
        print(f"   ✗ Movement error: {e}")
    
    # Test 7: Dance sequence
    print("\n7. Testing dance sequence...")
    try:
        dance_data = {
            "dance": "wave"
        }
        response = requests.post(f"{base_url}/dance", json=dance_data)
        if response.status_code == 200:
            print("   ✓ Dance sequence working")
            data = response.json()
            print(f"   Response: {data['message']}")
        else:
            print(f"   ✗ Dance failed: {response.status_code}")
    except Exception as e:
        print(f"   ✗ Dance error: {e}")
    
    # Test 8: Sensor data
    print("\n8. Testing sensor data...")
    try:
        response = requests.get(f"{base_url}/sensors")
        if response.status_code == 200:
            print("   ✓ Sensor data working")
            data = response.json()
            sensors = data['data']
            print(f"   Temperature: {sensors['temperature']}°C")
            print(f"   Distance: {sensors['ultrasonic_distance']}m")
            print(f"   Battery: {sensors['battery_voltage']}V")
        else:
            print(f"   ✗ Sensor data failed: {response.status_code}")
    except Exception as e:
        print(f"   ✗ Sensor data error: {e}")
    
    print("\n" + "=" * 40)
    print("🎉 API Testing Complete!")
    print("\n✅ Your STEM_XPERT Robot is working correctly!")
    print("\n🌐 You can now access the web interface at:")
    print("   http://localhost:8000")
    print("\n📱 Available interfaces:")
    print("   • Dashboard: Real-time robot monitoring")
    print("   • Robot Control: Manual servo and movement control")
    print("   • Programming: Blockly visual programming")
    print("   • Voice Control: Speech recognition and TTS")
    print("   • Face Recognition: Computer vision features")
    print("   • Education: Tutorials and learning modules")
    print("   • Settings: Robot configuration")
    
    return True

if __name__ == "__main__":
    test_api()
