#!/usr/bin/env python3
"""
STEM_XPERT Robot - Main Application Entry Point
Inspired by UBTech Alpha 1E with enhanced capabilities
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path

from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager

# Import core modules
from core.robot_controller import RobotController
from core.config import Config
from api.routes import router
from api.websocket_manager import WebSocketManager
from hardware.hardware_manager import HardwareManager
from ai.ai_engine import AIEngine

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global instances
robot_controller = None
websocket_manager = None
hardware_manager = None
ai_engine = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    global robot_controller, websocket_manager, hardware_manager, ai_engine
    
    try:
        # Initialize configuration
        config = Config()
        logger.info("Configuration loaded successfully")
        
        # Initialize hardware manager
        hardware_manager = HardwareManager(config)
        await hardware_manager.initialize()
        logger.info("Hardware manager initialized")
        
        # Initialize AI engine
        ai_engine = AIEngine(config)
        await ai_engine.initialize()
        logger.info("AI engine initialized")
        
        # Initialize WebSocket manager
        websocket_manager = WebSocketManager()
        logger.info("WebSocket manager initialized")
        
        # Initialize robot controller
        robot_controller = RobotController(
            config=config,
            hardware_manager=hardware_manager,
            ai_engine=ai_engine,
            websocket_manager=websocket_manager
        )
        await robot_controller.initialize()
        logger.info("Robot controller initialized")
        
        # Start background tasks
        asyncio.create_task(robot_controller.start_main_loop())
        logger.info("STEM_XPERT Robot started successfully!")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to initialize robot: {e}")
        raise
    finally:
        # Cleanup
        if robot_controller:
            await robot_controller.shutdown()
        if hardware_manager:
            await hardware_manager.cleanup()
        if ai_engine:
            await ai_engine.cleanup()
        logger.info("Robot shutdown complete")

# Create FastAPI application
app = FastAPI(
    title="STEM_XPERT Robot API",
    description="Educational Humanoid Robot Control System",
    version="1.0.0",
    lifespan=lifespan
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routes
app.include_router(router, prefix="/api/v1")

# Serve static files
static_path = Path(__file__).parent.parent / "frontend" / "build"
if static_path.exists():
    app.mount("/", StaticFiles(directory=static_path, html=True), name="static")

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time communication"""
    await websocket_manager.connect(websocket)
    try:
        while True:
            data = await websocket.receive_json()
            await websocket_manager.handle_message(websocket, data)
    except WebSocketDisconnect:
        websocket_manager.disconnect(websocket)

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "robot_status": robot_controller.get_status() if robot_controller else "initializing",
        "timestamp": asyncio.get_event_loop().time()
    }

def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    logger.info(f"Received signal {signum}, shutting down...")
    sys.exit(0)

if __name__ == "__main__":
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Run the application
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=False,  # Set to True for development
        log_level="info"
    )
