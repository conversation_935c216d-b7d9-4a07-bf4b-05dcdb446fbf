@echo off
REM STEM_XPERT Robot Startup Script for Windows
REM This script starts all robot services

setlocal enabledelayedexpansion

echo [%date% %time%] Starting STEM_XPERT Robot...

REM Get script directory
set SCRIPT_DIR=%~dp0
set PROJECT_DIR=%SCRIPT_DIR%..

echo Project directory: %PROJECT_DIR%

REM Check if virtual environment exists
if not exist "%PROJECT_DIR%\venv" (
    echo [WARNING] Virtual environment not found. Creating one...
    cd /d "%PROJECT_DIR%"
    python -m venv venv
    call venv\Scripts\activate.bat
    pip install -r requirements.txt
) else (
    echo Activating virtual environment...
    call "%PROJECT_DIR%\venv\Scripts\activate.bat"
)

REM Check Python dependencies
echo Checking Python dependencies...
python -c "import fastapi, uvicorn" 2>nul || (
    echo [ERROR] Required Python packages not found. Installing...
    pip install -r "%PROJECT_DIR%\requirements.txt"
)

REM Create necessary directories
echo Creating necessary directories...
if not exist "%PROJECT_DIR%\logs" mkdir "%PROJECT_DIR%\logs"
if not exist "%PROJECT_DIR%\models" mkdir "%PROJECT_DIR%\models"
if not exist "%PROJECT_DIR%\data" mkdir "%PROJECT_DIR%\data"

REM Set environment variables
set PYTHONPATH=%PROJECT_DIR%\backend;%PYTHONPATH%
set ROBOT_CONFIG_PATH=%PROJECT_DIR%\config\robot_config.yaml
set ROBOT_LOG_LEVEL=INFO

REM Start the backend server
echo Starting backend server...
cd /d "%PROJECT_DIR%\backend"
start "STEM_XPERT Backend" python main.py

REM Wait for server to start
timeout /t 5 /nobreak >nul

REM Check if frontend build exists
if exist "%PROJECT_DIR%\frontend\build" (
    echo Frontend build found - will be served by backend
) else (
    echo [WARNING] Frontend build not found. 
    if exist "%PROJECT_DIR%\frontend\package.json" (
        echo Starting frontend development server...
        cd /d "%PROJECT_DIR%\frontend"
        start "STEM_XPERT Frontend" npm start
    )
)

REM Display status
echo.
echo STEM_XPERT Robot is now running!
echo.
echo Robot Status:
echo    Backend API: http://localhost:8000
echo    WebSocket: ws://localhost:8000/ws
echo    Health Check: http://localhost:8000/health
echo.
echo Web Interface:
if exist "%PROJECT_DIR%\frontend\build" (
    echo    Production: http://localhost:8000
) else (
    echo    Development: http://localhost:3000
)
echo.
echo To stop the robot, close this window or run stop_robot.bat
echo.

pause
